# 📧 Simple Email Setup Guide (Nodemailer + Gmail)

## **✅ What We've Done**

1. ✅ **Installed nodemailer** with `pnpm install nodemailer @types/nodemailer`
2. ✅ **Created email service** with beautiful HTML templates
3. ✅ **Added auto-join organization** functionality
4. ✅ **Integrated email sending** in signup and invitation flows
5. ✅ **Fixed duplicate emails** - now sends beautiful custom emails instead of Auth0's default

---

## **🚀 Quick Setup (5 minutes)**

### **Step 1: Get Gmail App Password**

1. **Go to your Gmail account settings**:

   - Visit: https://myaccount.google.com/security
   - Enable 2-Factor Authentication (required)

2. **Create App Password**:
   - Go to: https://myaccount.google.com/apppasswords
   - Select "Mail" and "Other (custom name)"
   - Name it: "Talknician App"
   - **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### **Step 2: Add Environment Variables**

Add these to your `.env` file:

```bash
# Email Configuration (Gmail SMTP)
SMTP_USER=<EMAIL>
SMTP_PASS=abcd efgh ijkl mnop  # Your 16-character App Password
SMTP_FROM=<EMAIL>  # Or use SMTP_USER

# Frontend URL for email links
FRONTEND_URL=http://localhost:3000
```

### **Step 3: Test It!**

```bash
# Start your backend
cd talknician-lite-be
pnpm dev

# Test signup with a real email address
curl -X POST http://localhost:8000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "name": "Test User"
  }'
```

**You should receive a beautiful verification email!** 📧

---

## **📧 What Emails Are Sent**

### **1. Verification Email** (on signup)

- **When**: User registers
- **Contains**: Welcome message + verification link
- **Design**: Purple theme with Talknician branding

### **2. Invitation Email** (when inviting users)

- **When**: Admin invites someone to organization
- **Contains**: Organization name + join link
- **Design**: Green theme with team collaboration focus

### **3. Password Reset Email** (ready for future use)

- **When**: User requests password reset
- **Contains**: Secure reset link (expires in 1 hour)
- **Design**: Red theme with security messaging

---

## **🎨 Email Templates**

All emails include:

- ✅ **Responsive design** (mobile-friendly)
- ✅ **Beautiful HTML** with inline CSS
- ✅ **Fallback text** for email clients that don't support HTML
- ✅ **Professional branding** with Talknician colors
- ✅ **Clear call-to-action** buttons
- ✅ **Backup links** if buttons don't work

---

## **🔧 How It Works**

### **Auto-Join Flow**:

1. Admin invites `<EMAIL>` to organization
2. **Invitation email sent** with signup link
3. User clicks link and registers
4. **User automatically joins** the organization
5. **Verification email sent** to activate account

### **Email Service Features**:

- ✅ **Error handling** - won't break signup if email fails
- ✅ **Logging** - tracks all email attempts
- ✅ **Connection testing** - verifies SMTP setup
- ✅ **Flexible** - easy to switch to other providers later
- ✅ **Smart fallback** - uses Auth0 emails if custom service fails
- ✅ **No duplicates** - sends beautiful custom emails instead of Auth0's default

---

## **🚨 Troubleshooting**

### **"Authentication failed" error**:

1. Make sure 2FA is enabled on Gmail
2. Use App Password, not your regular password
3. Check SMTP_USER is your full Gmail address

### **"Connection timeout" error**:

1. Check your internet connection
2. Some networks block SMTP ports
3. Try from a different network

### **Emails not received**:

1. Check spam/junk folder
2. Verify email address is correct
3. Check server logs for errors

### **Gmail daily limits**:

- **Free Gmail**: 500 emails/day
- **Google Workspace**: 2000 emails/day
- **More than that**: Consider upgrading to Postmark/Mailgun

---

## **📊 Usage Monitoring**

Check your email sending in the logs:

```bash
# Backend logs will show:
✅ Email sent successfully: { to: '<EMAIL>', messageId: '...' }
❌ Failed to send email: { error: '...' }
```

---

## **🔄 Alternative SMTP Providers**

If Gmail doesn't work for you:

### **Outlook/Hotmail**:

```bash
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### **Yahoo**:

```bash
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### **Custom Domain** (if you have one):

```bash
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

---

## **🚀 Next Steps**

1. **Test the setup** with a real email
2. **Invite a team member** to test auto-join
3. **Monitor usage** and upgrade when needed
4. **Consider upgrading** to Postmark/Mailgun for production

**Your email system is now ready!** 🎉

The setup is super simple and will handle:

- ✅ User verification emails
- ✅ Organization invitations
- ✅ Auto-join functionality
- ✅ Beautiful, professional templates

**Want to upgrade later?** The email service is designed to easily switch to Postmark, Mailgun, or SendGrid when you're ready to scale!
