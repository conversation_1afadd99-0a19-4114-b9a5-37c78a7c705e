# Social Login Debug Guide

## Testing the Fix

### 1. Clear Previous State

```bash
# Clear browser localStorage
localStorage.clear()

# Or specifically clear auth data
localStorage.removeItem('auth_token')
localStorage.removeItem('auth_user')
localStorage.removeItem('@@auth0spajs@@::YWQLOwYeSlTaMeNqbMQzt0kGqC5l1kJZ::@@user@@')
```

### 2. Test Social Login

1. Go to `/login`
2. Click "Continue with Google"
3. Complete Google authentication
4. **Check browser console** for logs:
   ```
   Auth0 authenticated - processing social login
   New social login token detected, syncing...
   Social login sync successful
   ```

### 3. Verify localStorage

After login, check `localStorage`:

```javascript
// Should show your JWT token
console.log("Auth Token:", localStorage.getItem("auth_token"));

// Should show user data from backend
console.log("User Data:", localStorage.getItem("auth_user"));

// Auth0 cache should also exist
console.log(
  "Auth0 Cache:",
  localStorage.getItem(
    "@@auth0spajs@@::YWQLOwYeSlTaMeNqbMQzt0kGqC5l1kJZ::@@user@@"
  )
);
```

### 4. Test Account Linking

#### Option A: Email First, Then Social

1. Register with email: `<EMAIL>` / password `test123`
2. Complete setup, then logout
3. Click "Continue with Google" using the same email
4. **Expected**: Should login to same account with same organizations

#### Option B: Social First, Then Email

1. Login with Google using `<EMAIL>`
2. Complete organization setup, then logout
3. Try registering with email: `<EMAIL>` / password
4. **Expected**: Should show appropriate message about existing account

### 5. Backend Logs to Watch For

```bash
# In talknician-lite-be terminal
tail -f combined.log | grep -E "(ACCOUNT LINKED|social-login-sync|Token verification)"
```

Expected logs:

```json
{"message": "Token verification successful", "sub": "google-oauth2|..."}
{"message": "🔗 ACCOUNT LINKED - user found by email", "accountLinking": true}
{"message": "Social login sync completed"}
```

## Common Issues

### Issue: Auth token still disappearing

**Check**: Browser console for any error messages during sync
**Solution**: Check if backend is running and accessible

### Issue: 401 Unauthorized on social-login-sync

**Check**: Token verification in backend logs
**Solution**: Verify Auth0 configuration and JWT verification

### Issue: Account not linking properly

**Check**: Backend logs for "ACCOUNT LINKED" message
**Solution**: Verify email matches exactly between accounts

## Debugging Commands

```javascript
// Check Auth0 state
import { isAuthenticated, getUser, getToken } from "@/lib/auth0";

// In browser console after login
isAuthenticated().then(console.log); // Should be true
getUser().then(console.log); // Should show user data
getToken().then(console.log); // Should show JWT token
```

## Success Indicators

✅ **Fixed**: `auth_token` persists in localStorage after social login  
✅ **Fixed**: Console shows successful sync messages  
✅ **Working**: Account linking happens automatically  
✅ **Working**: User can use either login method  
✅ **Working**: Organizations preserved during account linking

## Need Help?

If issues persist, provide:

1. Browser console logs (during login)
2. Backend logs (during social-login-sync call)
3. localStorage contents after login attempt
4. Specific error messages
