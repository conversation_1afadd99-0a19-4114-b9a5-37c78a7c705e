# Fix Auth0 "no-email" Issue - JWT Token Missing Email Claim

## 🔍 **Problem Identified**

Your JWT tokens from Auth0 social login show `"email":"no-email"` because **Auth0 doesn't include email claims in JWT tokens by default**. This is a configuration issue, not a code issue.

From your logs:

```
info: Token verification successful {"email":"no-email","service":"talknician-lite-api","sub":"google-oauth2|107632961209860765890"}
```

## 🔧 **Solution: Add Auth0 Rule to Include Email in JWT**

### Step 1: Login to Auth0 Dashboard

1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Select your tenant/application

### Step 2: Create Auth0 Rule (Legacy) or Action (New)

#### For Auth0 Rules (Legacy):

1. Go to **Auth Pipeline** → **Rules**
2. Click **Create Rule**
3. Choose **Empty Rule**
4. Name it: `Add Email to Access Token`
5. Paste this code:

```javascript
function (user, context, callback) {
  // Add email to Access Token for API consumption
  const namespace = 'https://api.talknician.com/';

  // Add email to Access Token (this is what your backend API receives)
  context.accessToken[namespace + 'email'] = user.email;
  context.accessToken['email'] = user.email; // Also add without namespace for compatibility

  // Optionally add to ID Token as well
  context.idToken[namespace + 'email'] = user.email;
  context.idToken['email'] = user.email;

  // Add other useful claims
  context.accessToken[namespace + 'name'] = user.name;
  context.accessToken['name'] = user.name;

  callback(null, user, context);
}
```

#### For Auth0 Actions (New):

1. Go to **Actions** → **Flows**
2. Click **Login**
3. Drag **Custom** action to the flow
4. Create new action with this code:

```javascript
exports.onExecutePostLogin = async (event, api) => {
  const namespace = "https://api.talknician.com/";

  // Add email to Access Token
  api.accessToken.setCustomClaim(`${namespace}email`, event.user.email);
  api.accessToken.setCustomClaim("email", event.user.email);

  // Add to ID Token
  api.idToken.setCustomClaim(`${namespace}email`, event.user.email);
  api.idToken.setCustomClaim("email", event.user.email);

  // Add name
  api.accessToken.setCustomClaim(`${namespace}name`, event.user.name);
  api.accessToken.setCustomClaim("name", event.user.name);
};
```

### Step 3: Save and Deploy

1. Click **Save**
2. **Deploy** the rule/action
3. The rule will now run for all logins

## 🔧 **Backend Fix Applied**

I've already updated your backend (`auth0.service.ts`) to handle email claims from multiple locations:

```typescript
// Extract email from various possible locations in the token
const payload = verifiedPayload as any;
const email =
  payload.email ||
  payload["https://api.talknician.com/email"] ||
  payload["https://api.exampleco.com/email"] ||
  "no-email";
```

This will check for email in:

1. `payload.email` (direct claim)
2. `payload['https://api.talknician.com/email']` (namespaced claim)
3. `payload['https://api.exampleco.com/email']` (example namespace)

## 🧪 **Testing the Fix**

### Step 1: Clear Auth0 Cache

```javascript
// In browser console
localStorage.clear();
```

### Step 2: Test Social Login

1. Login with Google/Microsoft
2. Check backend logs - should now show:

```
info: Token verification successful {"email":"<EMAIL>","sub":"google-oauth2|107632961209860765890"}
```

### Step 3: Verify User Creation

The social login should now automatically create users because:

1. ✅ JWT token contains email claim
2. ✅ Backend can extract email properly
3. ✅ `social-login-sync` endpoint gets valid email
4. ✅ User creation succeeds

## 🔍 **Why This Happens**

Auth0 follows OpenID Connect (OIDC) standards where:

- **ID Tokens** contain user profile information (including email)
- **Access Tokens** are for API authorization and don't include profile data by default

For API consumption, you need to explicitly add claims to Access Tokens using Rules/Actions.

## 🎯 **Expected Results After Fix**

### Before (Current):

```json
{
  "sub": "google-oauth2|107632961209860765890",
  "aud": [
    "http://localhost:8000/",
    "https://dev-55elwor2inabc85m.us.auth0.com/userinfo"
  ],
  "iss": "https://dev-55elwor2inabc85m.us.auth0.com/",
  "exp": 1750763085
  // ❌ No email claim
}
```

### After (Fixed):

```json
{
  "sub": "google-oauth2|107632961209860765890",
  "aud": [
    "http://localhost:8000/",
    "https://dev-55elwor2inabc85m.us.auth0.com/userinfo"
  ],
  "iss": "https://dev-55elwor2inabc85m.us.auth0.com/",
  "exp": 1750763085,
  "email": "<EMAIL>",
  "name": "Your Name",
  "https://api.talknician.com/email": "<EMAIL>"
  // ✅ Email claims included
}
```

## 🚀 **Next Steps**

1. **Create the Auth0 Rule/Action** (most important)
2. **Test social login** - should work automatically
3. **Monitor logs** - should see actual email instead of "no-email"
4. **Verify user creation** - new social users should be created in database

## 📚 **Reference**

This is a common Auth0 configuration issue. The Auth0 documentation recommends adding custom claims to Access Tokens when APIs need user profile information.

- [Auth0 Rules Documentation](https://auth0.com/docs/rules)
- [Auth0 Actions Documentation](https://auth0.com/docs/actions)
- [Custom Claims Guide](https://auth0.com/docs/tokens/guides/create-namespaced-custom-claims)

## ✅ **Summary**

The "no-email" issue is **not a code problem** - it's an **Auth0 configuration issue**. By adding the Rule/Action above, your JWT tokens will include email claims, and your existing backend fixes will handle the rest automatically.
