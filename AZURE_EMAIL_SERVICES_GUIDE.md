# 📧 Azure Email Services Guide

## **Issue Fixed: Auto-Join Organization** ✅

**Problem**: Users registering were not automatically joining organizations they were invited to.

**Solution**: Added auto-join logic to the signup endpoint that:

1. Checks for pending invitations for the user's email
2. Automatically adds them to organizations
3. Marks invitations as used
4. Updates the response message

**How it works now**:

```javascript
// During signup, the system now:
1. Creates user account
2. Finds pending invitations for their email
3. Auto-joins them to invited organizations
4. Shows success message: "Account created successfully. You've been automatically added to: Acme Corp, Tech Startup."
```

---

## **Azure Email Service Recommendations**

### **🏆 1. Azure Communication Services Email (RECOMMENDED)**

**Best for**: Modern applications, high deliverability, Azure-native

**Pricing**:

- **FREE**: 1,000 emails/month
- **Paid**: $0.000025 per email (very cheap)

**Pros**:

- ✅ Native Azure integration
- ✅ Excellent deliverability rates
- ✅ Built-in analytics and tracking
- ✅ Programmatic API
- ✅ Support for both transactional and marketing emails
- ✅ Built-in domain verification
- ✅ Webhook support for email events

**Setup**:

```bash
# 1. Create Communication Services resource
az communication create \
  --name "talknician-email" \
  --resource-group "talknician-rg" \
  --data-location "United States"

# 2. Create Email Communication Service
az communication email create \
  --name "talknician-email-service" \
  --resource-group "talknician-rg" \
  --data-location "United States"
```

**Integration Example**:

```javascript
import { EmailClient } from "@azure/communication-email";

const emailClient = new EmailClient(connectionString);

const sendEmail = async (to, subject, htmlContent) => {
  const emailMessage = {
    senderAddress: "<EMAIL>",
    content: {
      subject: subject,
      html: htmlContent,
    },
    recipients: {
      to: [{ address: to }],
    },
  };

  const poller = await emailClient.beginSend(emailMessage);
  const response = await poller.pollUntilDone();
  return response;
};
```

---

### **🥈 2. SendGrid (Azure Marketplace)**

**Best for**: Established email platform, rich features

**Pricing**:

- **FREE**: 100 emails/day
- **Essentials**: $14.95/month (50k emails)
- **Pro**: $89.95/month (1.5M emails)

**Pros**:

- ✅ Battle-tested platform
- ✅ Advanced analytics and A/B testing
- ✅ Template management
- ✅ Excellent documentation
- ✅ Available in Azure Marketplace
- ✅ Marketing automation features

**Setup via Azure**:

```bash
# Deploy from Azure Marketplace
az deployment group create \
  --resource-group talknician-rg \
  --template-uri "sendgrid-marketplace-template" \
  --parameters accountName="talknician-sendgrid"
```

**Integration**:

```javascript
import sgMail from "@sendgrid/mail";

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const sendEmail = async (to, subject, html) => {
  const msg = {
    to,
    from: "<EMAIL>",
    subject,
    html,
  };

  await sgMail.send(msg);
};
```

---

### **🥉 3. Azure Logic Apps + Office 365**

**Best for**: Office 365/Microsoft 365 environments

**Pricing**:

- **Consumption**: $0.000025 per action
- **Standard**: $0.12 per workflow/month

**Pros**:

- ✅ No-code/low-code solution
- ✅ Deep Office 365 integration
- ✅ Visual workflow designer
- ✅ Built-in connectors

**Cons**:

- ❌ Less programmatic control
- ❌ Dependent on Office 365
- ❌ Can be complex for simple email sending

---

### **🔧 4. Custom SMTP with Azure**

**Best for**: Existing SMTP providers, budget-conscious

**Options**:

- **Mailgun**: $0.80/1000 emails
- **Postmark**: $1.25/1000 emails
- **Amazon SES**: $0.10/1000 emails

**Setup**:

```javascript
import nodemailer from "nodemailer";

const transporter = nodemailer.createTransporter({
  host: "smtp.mailgun.org",
  port: 587,
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

const sendEmail = async (to, subject, html) => {
  await transporter.sendMail({
    from: "<EMAIL>",
    to,
    subject,
    html,
  });
};
```

---

## **📋 Implementation Checklist**

### **Environment Variables Needed**:

```bash
# Azure Communication Services
AZURE_COMMUNICATION_CONNECTION_STRING="endpoint=https://..."
AZURE_EMAIL_SENDER_ADDRESS="<EMAIL>"

# OR SendGrid
SENDGRID_API_KEY="SG.xyz..."
SENDGRID_FROM_EMAIL="<EMAIL>"

# OR SMTP
SMTP_HOST="smtp.provider.com"
SMTP_PORT="587"
SMTP_USER="username"
SMTP_PASS="password"
SMTP_FROM="<EMAIL>"
```

### **Email Service Class**:

```javascript
// src/services/email.service.ts
export class EmailService {
  async sendVerificationEmail(to: string, verificationLink: string) {
    const html = `
      <h2>Verify Your Email</h2>
      <p>Click the link below to verify your email address:</p>
      <a href="${verificationLink}">Verify Email</a>
    `;

    await this.send(to, "Verify Your Email", html);
  }

  async sendInvitationEmail(to: string, organizationName: string, inviteLink: string) {
    const html = `
      <h2>You're Invited to Join ${organizationName}</h2>
      <p>Click the link below to join:</p>
      <a href="${inviteLink}">Join Organization</a>
    `;

    await this.send(to, `Invitation to ${organizationName}`, html);
  }

  async sendPasswordResetEmail(to: string, resetLink: string) {
    const html = `
      <h2>Reset Your Password</h2>
      <p>Click the link below to reset your password:</p>
      <a href="${resetLink}">Reset Password</a>
    `;

    await this.send(to, "Reset Your Password", html);
  }

  private async send(to: string, subject: string, html: string) {
    // Implementation depends on chosen service
  }
}
```

---

## **🎯 Final Recommendation**

**For Talknician**: I recommend **Azure Communication Services Email** because:

1. **Cost-effective**: 1,000 free emails/month, then $0.000025 per email
2. **Azure-native**: Perfect integration with your existing Azure infrastructure
3. **High deliverability**: Microsoft's email infrastructure
4. **Scalable**: Handles from startup to enterprise levels
5. **Developer-friendly**: Clean REST API and SDKs

**Next Steps**:

1. Test the auto-join fix by registering a new user with a pending invitation
2. Set up Azure Communication Services Email resource
3. Configure domain verification
4. Update your email templates
5. Add email service to your backend

**Quick Test**:

```bash
# Test auto-join functionality
curl -X POST http://localhost:8000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "name": "Test User"
  }'
```
