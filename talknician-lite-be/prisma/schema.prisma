generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String               @id @default(cuid())
  email                   String               @unique
  name                    String?
  auth0Id                 String               @unique @map("auth0_id")
  avatar                  String?
  createdAt               DateTime             @default(now()) @map("created_at")
  updatedAt               DateTime             @updatedAt @map("updated_at")
  conversations           Conversation[]
  documents               Document[]
  unifiedMessages         UnifiedMessage[]
  organizationMemberships OrganizationMember[]
  userSessions            UserSession[]
  scrapedWebsites         ScrapedWebsite[]

  @@map("users")
}

model Organization {
  id              String                   @id @default(cuid())
  name            String
  slug            String                   @unique
  description     String?
  avatar          String?
  vectorStoreId   String?                  @unique @map("vector_store_id")
  createdAt       DateTime                 @default(now()) @map("created_at")
  plan            PlanType                 @default(FREE)
  updatedAt       DateTime                 @updatedAt @map("updated_at")
  conversations   Conversation[]
  documents       Document[]
  members         OrganizationMember[]
  invitations     OrganizationInvitation[]
  chatSettings    ChatSettings[]
  scrapedWebsites ScrapedWebsite[]

  @@map("organizations")
}

model OrganizationMember {
  id                  String                @id @default(cuid())
  role                OrganizationRole
  status              MembershipStatus      @default(ACTIVE)
  joinedAt            DateTime              @default(now()) @map("joined_at")
  organizationId      String                @map("organization_id")
  userId              String                @map("user_id")
  applicationMessage  String?               @map("application_message")
  reviewedBy          String?               @map("reviewed_by")
  reviewedAt          DateTime?             @map("reviewed_at")
  reviewMessage       String?               @map("review_message")
  organization        Organization          @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user                User                  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@map("organization_members")
}

model OrganizationInvitation {
  id             String           @id @default(cuid())
  email          String
  role           OrganizationRole
  invitedBy      String           @map("invited_by")
  organizationId String           @map("organization_id")
  createdAt      DateTime         @default(now()) @map("created_at")
  expiresAt      DateTime         @map("expires_at")
  usedAt         DateTime?        @map("used_at")
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([email, organizationId])
  @@map("organization_invitations")
}

model Conversation {
  id              String           @id @default(cuid())
  title           String
  createdAt       DateTime         @default(now()) @map("created_at")
  isArchived      Boolean          @default(false) @map("is_archived")
  organizationId  String           @map("organization_id")
  updatedAt       DateTime         @updatedAt @map("updated_at")
  userId          String           @map("user_id")
  organization    Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  unifiedMessages UnifiedMessage[]

  @@map("conversations")
}

model Document {
  id                String              @id @default(cuid())
  filename          String
  size              Int
  azureBlobUrl      String?             @map("azure_blob_url")
  localPath         String?             @map("local_path")
  openaiFileId      String?             @unique @map("openai_file_id")
  createdAt         DateTime            @default(now()) @map("created_at")
  mimeType          String              @map("mime_type")
  organizationId    String              @map("organization_id")
  originalName      String              @map("original_name")
  status            DocumentStatus      @default(PROCESSING)
  updatedAt         DateTime            @updatedAt @map("updated_at")
  userId            String              @map("user_id")
  uploadedBy        String              @map("uploaded_by")
  isDeleted         Boolean             @default(false) @map("is_deleted")
  embeddings        DocumentEmbedding[]
  organization      Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  scrapedWebsite    ScrapedWebsite?
  messageAnnotations MessageAnnotation[]
  messageReferences MessageReference[]

  @@map("documents")
}

model DocumentEmbedding {
  id         String   @id @default(cuid())
  documentId String   @map("document_id")
  content    String
  embedding  Float[]
  metadata   Json?
  createdAt  DateTime @default(now()) @map("created_at")
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_embeddings")
}

model UserSession {
  id           String    @id @default(cuid())
  userId       String    @map("user_id")
  refreshToken String    @unique @map("refresh_token")
  expiresAt    DateTime  @map("expires_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  revokedAt    DateTime? @map("revoked_at")
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model ChatSettings {
  id               String       @id @default(cuid())
  userId           String       @map("user_id")
  organizationId   String       @map("organization_id")
  enableWebSearch  Boolean      @default(false) @map("enable_web_search")
  personality      String?
  systemPrompt     String?      @map("system_prompt")
  createdAt        DateTime     @default(now()) @map("created_at")
  updatedAt        DateTime     @updatedAt @map("updated_at")
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@map("chat_settings")
}

model ScrapedWebsite {
  id                String              @id @default(cuid())
  url               String
  title             String
  content           String
  markdown          String?
  metadata          Json?
  documentId        String?             @unique @map("document_id")
  organizationId    String              @map("organization_id")
  userId            String              @map("user_id")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")
  status            DocumentStatus      @default(PROCESSING)
  organization      Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  document          Document?           @relation(fields: [documentId], references: [id], onDelete: SetNull)
  messageReferences MessageReference[]
  messageAnnotations MessageAnnotation[]

  @@map("scraped_websites")
}

// New UnifiedMessage system models
model UnifiedMessage {
  id               String             @id @default(cuid())
  role             MessageRole
  content          String
  conversationId   String             @map("conversation_id")
  userId           String             @map("user_id")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @updatedAt @map("updated_at")
  openaiMessageId  String?            @map("openai_message_id")
  openaiItemId     String?            @map("openai_item_id")
  sequenceNumber   Int?               @map("sequence_number")
  metadata         Json?
  conversation     Conversation       @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user             User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  annotations      MessageAnnotation[]
  references       MessageReference[]

  @@map("unified_messages")
}

model MessageAnnotation {
  id               String         @id @default(cuid())
  type             AnnotationType
  startIndex       Int            @map("start_index")
  endIndex         Int            @map("end_index")
  url              String?
  title            String?
  fileId           String?        @map("file_id")
  filename         String?
  documentId       String?        @map("document_id")
  websiteId        String?        @map("website_id")
  unifiedMessageId String         @map("unified_message_id")
  unifiedMessage   UnifiedMessage @relation(fields: [unifiedMessageId], references: [id], onDelete: Cascade)
  document         Document?      @relation(fields: [documentId], references: [id], onDelete: SetNull)
  website          ScrapedWebsite? @relation(fields: [websiteId], references: [id], onDelete: SetNull)

  @@map("message_annotations")
}

model MessageReference {
  id               String         @id @default(cuid())
  type             ReferenceType
  title            String
  documentId       String?        @map("document_id")
  azureBlobUrl     String?        @map("azure_blob_url")
  openaiFileId     String?        @map("openai_file_id")
  filename         String?
  websiteId        String?        @map("website_id")
  url              String?
  scrapedContent   String?        @map("scraped_content")
  metadata         Json?
  createdAt        DateTime       @default(now()) @map("created_at")
  unifiedMessageId String         @map("unified_message_id")
  unifiedMessage   UnifiedMessage @relation(fields: [unifiedMessageId], references: [id], onDelete: Cascade)
  document         Document?      @relation(fields: [documentId], references: [id], onDelete: SetNull)
  website          ScrapedWebsite? @relation(fields: [websiteId], references: [id], onDelete: SetNull)

  @@map("message_references")
}

enum PlanType {
  FREE
  PRO
  ENTERPRISE
}

enum OrganizationRole {
  OWNER
  MANAGER
  MEMBER
}

enum MembershipStatus {
  PENDING
  ACTIVE
  REJECTED
}

enum MessageRole {
  user
  assistant
  developer
}

enum DocumentStatus {
  PROCESSING
  COMPLETED
  FAILED
}

enum AnnotationType {
  URL_CITATION
  FILE_CITATION
}

enum ReferenceType {
  DOCUMENT
  WEBSITE
}
