const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function checkSpecificUser() {
  try {
    const googleAuth0Id = "google-oauth2|107632961209860765890";

    console.log(`🔍 Looking for user with Auth0 ID: ${googleAuth0Id}`);

    const user = await prisma.user.findUnique({
      where: { auth0Id: googleAuth0Id },
      include: {
        organizationMemberships: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (user) {
      console.log("✅ User found:");
      console.log(`  ID: ${user.id}`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Auth0 ID: ${user.auth0Id}`);
      console.log(`  Name: ${user.name}`);
      console.log(`  Organizations: ${user.organizationMemberships.length}`);

      if (user.organizationMemberships.length > 0) {
        user.organizationMemberships.forEach((membership) => {
          console.log(
            `    - ${membership.organization.name} (${membership.role})`
          );
        });
      }
    } else {
      console.log("❌ User NOT found in database");

      // Check if there's a user with similar email
      console.log(
        '\n🔍 Checking for users with "<EMAIL>" email...'
      );
      const emailUser = await prisma.user.findUnique({
        where: { email: "<EMAIL>" },
      });

      if (emailUser) {
        console.log("✅ Found user with that email:");
        console.log(`  ID: ${emailUser.id}`);
        console.log(`  Email: ${emailUser.email}`);
        console.log(`  Auth0 ID: ${emailUser.auth0Id}`);
        console.log(`  Name: ${emailUser.name}`);
      } else {
        console.log("❌ No user found with that email either");
      }
    }
  } catch (error) {
    console.error("❌ Error checking user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpecificUser();
