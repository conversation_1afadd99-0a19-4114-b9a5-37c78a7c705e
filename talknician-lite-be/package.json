{"name": "talknician-lite-be", "version": "1.0.0", "description": "Talknician Lite Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "@prisma/client": "^5.7.1", "@types/express-session": "^1.18.2", "@types/express-sse": "^0.5.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-jwt": "^8.4.1", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-sse": "^1.0.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "openai": "^4.20.1", "prisma": "^5.7.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "jest": "^30.0.2", "nodemon": "^3.0.2", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["express", "typescript", "auth0", "prisma", "postgresql", "api"], "author": "Talknician Team", "license": "MIT"}