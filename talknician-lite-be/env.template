# Environment
NODE_ENV=development
PORT=8000

# Database
DATABASE_URL="postgresql://your-username:<EMAIL>:5432/talknician-lite?schema=public&sslmode=require"

# Auth0 Configuration
AUTH0_DOMAIN=dev-55elwor2inabc85m.us.auth0.com
AUTH0_CLIENT_ID=48iarxFOApYHEvg2ZD0inMb7uxdKXVTB
AUTH0_CLIENT_SECRET=****************************************************************
AUTH0_AUDIENCE=http://localhost:8000/
AUTH0_ALGORITHM=RS256

# Management API (for user creation)
AUTH0_MANAGEMENT_CLIENT_ID=your-management-client-id
AUTH0_MANAGEMENT_CLIENT_SECRET=your-management-client-secret

# JWT Secret for internal tokens
JWT_SECRET=your-super-secret-jwt-key-here

# Frontend URL
FRONTEND_URL=http://localhost:3000

# OpenAI Configuration
OPENAI_API_KEY=************************************************************************************

# Azure Storage Configuration
AZURE_STORAGE_ACCOUNT_NAME=litedevelopment
AZURE_STORAGE_ACCOUNT_KEY=****************************************************************************************
AZURE_STORAGE_CONTAINER_NAME=talknician-uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info 