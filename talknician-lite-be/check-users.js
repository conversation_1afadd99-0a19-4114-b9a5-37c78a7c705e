const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log("🔍 Checking all users in database...");

    const users = await prisma.user.findMany({
      include: {
        organizationMemberships: {
          include: {
            organization: true,
          },
        },
      },
    });

    console.log(`📊 Found ${users.length} users:`);

    users.forEach((user, index) => {
      console.log(`\n👤 User ${index + 1}:`);
      console.log(`  ID: ${user.id}`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Auth0 ID: ${user.auth0Id}`);
      console.log(`  Name: ${user.name}`);
      console.log(`  Organizations: ${user.organizationMemberships.length}`);

      if (user.organizationMemberships.length > 0) {
        user.organizationMemberships.forEach((membership) => {
          console.log(
            `    - ${membership.organization.name} (${membership.role})`
          );
        });
      }
    });

    if (users.length === 0) {
      console.log("❌ No users found in database");
    }
  } catch (error) {
    console.error("❌ Error checking users:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
