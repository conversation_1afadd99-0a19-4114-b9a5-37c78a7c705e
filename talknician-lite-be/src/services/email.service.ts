import nodemailer from "nodemailer";
import { logger } from "@/utils/logger";

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS, // Use App Password for Gmail
      },
    });
  }

  async sendEmail(to: string, subject: string, html: string, text?: string) {
    try {
      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: to,
        subject: subject,
        text: text,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info("Email sent successfully", {
        to,
        subject,
        messageId: result.messageId,
      });
      return result;
    } catch (error: any) {
      logger.error("Failed to send email", {
        to,
        subject,
        error: error.message,
      });
      throw error;
    }
  }

  async sendVerificationEmail(to: string, verificationLink: string) {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
          <h1 style="color: #4F46E5; margin-bottom: 30px;">Verify Your Email</h1>
          <p style="font-size: 16px; margin-bottom: 30px;">
            Welcome to Talknician! Please click the button below to verify your email address and activate your account.
          </p>
          <a href="${verificationLink}" 
             style="background: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; margin-bottom: 20px;">
            Verify Email Address
          </a>
          <p style="font-size: 14px; color: #666; margin-top: 30px;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${verificationLink}" style="color: #4F46E5; word-break: break-all;">${verificationLink}</a>
          </p>
          <p style="font-size: 12px; color: #999; margin-top: 30px;">
            This verification link will expire in 24 hours.
          </p>
        </div>
      </body>
      </html>
    `;

    const text = `
      Welcome to Talknician!
      
      Please verify your email address by clicking this link:
      ${verificationLink}
      
      If the link doesn't work, copy and paste it into your browser.
      
      This verification link will expire in 24 hours.
    `;

    return await this.sendEmail(
      to,
      "Verify Your Email - Talknician",
      html,
      text
    );
  }

  async sendInvitationEmail(
    to: string,
    organizationName: string,
    inviteLink: string
  ) {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>You're Invited to Join ${organizationName}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
          <h1 style="color: #10B981; margin-bottom: 30px;">You're Invited!</h1>
          <p style="font-size: 18px; margin-bottom: 20px;">
            You've been invited to join <strong>${organizationName}</strong> on Talknician.
          </p>
          <p style="font-size: 16px; margin-bottom: 30px; color: #666;">
            Join your team and start collaborating with AI-powered conversations.
          </p>
          <a href="${inviteLink}" 
             style="background: #10B981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; margin-bottom: 20px;">
            Join ${organizationName}
          </a>
          <p style="font-size: 14px; color: #666; margin-top: 30px;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${inviteLink}" style="color: #10B981; word-break: break-all;">${inviteLink}</a>
          </p>
          <p style="font-size: 12px; color: #999; margin-top: 30px;">
            This invitation will expire in 7 days.
          </p>
        </div>
      </body>
      </html>
    `;

    const text = `
      You're invited to join ${organizationName} on Talknician!
      
      Click this link to join your team:
      ${inviteLink}
      
      If the link doesn't work, copy and paste it into your browser.
      
      This invitation will expire in 7 days.
    `;

    return await this.sendEmail(
      to,
      `Invitation to join ${organizationName} - Talknician`,
      html,
      text
    );
  }

  async sendPasswordResetEmail(to: string, resetLink: string) {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
          <h1 style="color: #EF4444; margin-bottom: 30px;">Reset Your Password</h1>
          <p style="font-size: 16px; margin-bottom: 30px;">
            We received a request to reset your password for your Talknician account.
          </p>
          <a href="${resetLink}" 
             style="background: #EF4444; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; margin-bottom: 20px;">
            Reset Password
          </a>
          <p style="font-size: 14px; color: #666; margin-top: 30px;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${resetLink}" style="color: #EF4444; word-break: break-all;">${resetLink}</a>
          </p>
          <p style="font-size: 12px; color: #999; margin-top: 30px;">
            This reset link will expire in 1 hour. If you didn't request this, please ignore this email.
          </p>
        </div>
      </body>
      </html>
    `;

    const text = `
      Reset Your Password - Talknician
      
      We received a request to reset your password.
      
      Click this link to reset your password:
      ${resetLink}
      
      If the link doesn't work, copy and paste it into your browser.
      
      This reset link will expire in 1 hour. If you didn't request this, please ignore this email.
    `;

    return await this.sendEmail(
      to,
      "Reset Your Password - Talknician",
      html,
      text
    );
  }

  async testConnection() {
    try {
      await this.transporter.verify();
      logger.info("Email service connection verified successfully");
      return true;
    } catch (error: any) {
      logger.error("Email service connection failed", { error: error.message });
      return false;
    }
  }
}

export const emailService = new EmailService();
