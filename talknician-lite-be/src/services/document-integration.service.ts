import { prisma } from "../config/database";
import { logger } from "../utils/logger";
import { azureStorageService } from "./azure-storage.service";
import OpenAI from "openai";

/**
 * Service to ensure proper document integration between Azure Storage and OpenAI
 * This service ensures documents are always saved to Azure first, then OpenAI,
 * with proper URL tracking for frontend display
 */
export class DocumentIntegrationService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Verify document has both Azure and OpenAI integration
   */
  async verifyDocumentIntegration(documentId: string): Promise<{
    hasAzureUrl: boolean;
    hasOpenAIFile: boolean;
    azureBlobUrl?: string;
    openaiFileId?: string;
    isComplete: boolean;
  }> {
    try {
      const document = await prisma.document.findUnique({
        where: { id: documentId },
      });

      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      const hasAzureUrl = !!document.azureBlobUrl;
      const hasOpenAIFile = !!document.openaiFileId;

      return {
        hasAzureUrl,
        hasOpenAIFile,
        azureBlobUrl: document.azureBlobUrl || undefined,
        openaiFileId: document.openaiFileId || undefined,
        isComplete: hasAzureUrl && hasOpenAIFile,
      };
    } catch (error) {
      logger.error(`Error verifying document integration: ${documentId}`, error);
      throw error;
    }
  }

  /**
   * Get document URLs for frontend display
   */
  async getDocumentUrls(documentId: string): Promise<{
    azureBlobUrl?: string;
    openaiFileId?: string;
    downloadUrl?: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
  }> {
    try {
      const document = await prisma.document.findUnique({
        where: { id: documentId },
      });

      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      // Generate download URL if Azure URL exists
      let downloadUrl: string | undefined;
      if (document.azureBlobUrl) {
        // For now, we'll use the Azure URL directly
        // In production, you might want to generate a SAS token for secure access
        downloadUrl = document.azureBlobUrl;
      }

      return {
        azureBlobUrl: document.azureBlobUrl || undefined,
        openaiFileId: document.openaiFileId || undefined,
        downloadUrl,
        filename: document.filename,
        originalName: document.originalName,
        mimeType: document.mimeType,
        size: document.size,
      };
    } catch (error) {
      logger.error(`Error getting document URLs: ${documentId}`, error);
      throw error;
    }
  }

  /**
   * Ensure document has OpenAI integration (if it only has Azure)
   */
  async ensureOpenAIIntegration(
    documentId: string,
    organizationId: string
  ): Promise<{ success: boolean; openaiFileId?: string; message: string }> {
    try {
      const document = await prisma.document.findUnique({
        where: { id: documentId },
      });

      if (!document) {
        return {
          success: false,
          message: "Document not found",
        };
      }

      // Check if already has OpenAI integration
      if (document.openaiFileId) {
        return {
          success: true,
          openaiFileId: document.openaiFileId,
          message: "Document already has OpenAI integration",
        };
      }

      // Check if has Azure URL
      if (!document.azureBlobUrl) {
        return {
          success: false,
          message: "Document must have Azure URL before OpenAI integration",
        };
      }

      // Get organization vector store
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        return {
          success: false,
          message: "Organization not found",
        };
      }

      // Create vector store if it doesn't exist
      let vectorStoreId = organization.vectorStoreId;
      if (!vectorStoreId) {
        const vectorStore = await this.openai.vectorStores.create({
          name: `${organization.name || "Organization"} Knowledge Base`,
          expires_after: {
            anchor: "last_active_at",
            days: 365,
          },
        });

        await prisma.organization.update({
          where: { id: organizationId },
          data: { vectorStoreId: vectorStore.id },
        });

        vectorStoreId = vectorStore.id;
      }

      // Get file stream from Azure
      const fileStream = await azureStorageService.getFileStream(document.azureBlobUrl);

      // Upload to OpenAI
      const openaiFile = await this.openai.files.create({
        file: fileStream as any,
        purpose: "assistants",
      });

      // Add to vector store
      await this.openai.vectorStores.files.create(vectorStoreId, {
        file_id: openaiFile.id,
      });

      // Update document record
      await prisma.document.update({
        where: { id: documentId },
        data: {
          openaiFileId: openaiFile.id,
          status: "COMPLETED",
        },
      });

      logger.info(`Added OpenAI integration to document: ${documentId}`, {
        openaiFileId: openaiFile.id,
        vectorStoreId,
      });

      return {
        success: true,
        openaiFileId: openaiFile.id,
        message: "Successfully added OpenAI integration",
      };
    } catch (error) {
      logger.error(`Error ensuring OpenAI integration: ${documentId}`, error);
      return {
        success: false,
        message: `Failed to add OpenAI integration: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  /**
   * Get all documents with their integration status for an organization
   */
  async getOrganizationDocumentsWithStatus(organizationId: string): Promise<
    Array<{
      id: string;
      filename: string;
      originalName: string;
      size: number;
      status: string;
      createdAt: string;
      hasAzureUrl: boolean;
      hasOpenAIFile: boolean;
      azureBlobUrl?: string;
      openaiFileId?: string;
      isComplete: boolean;
    }>
  > {
    try {
      const documents = await prisma.document.findMany({
        where: {
          organizationId,
          isDeleted: false,
        },
        orderBy: { createdAt: "desc" },
      });

      return documents.map((doc) => ({
        id: doc.id,
        filename: doc.filename,
        originalName: doc.originalName,
        size: doc.size,
        status: doc.status,
        createdAt: doc.createdAt.toISOString(),
        hasAzureUrl: !!doc.azureBlobUrl,
        hasOpenAIFile: !!doc.openaiFileId,
        azureBlobUrl: doc.azureBlobUrl || undefined,
        openaiFileId: doc.openaiFileId || undefined,
        isComplete: !!doc.azureBlobUrl && !!doc.openaiFileId,
      }));
    } catch (error) {
      logger.error(
        `Error getting organization documents with status: ${organizationId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Validate document integration for UnifiedMessage references
   */
  async validateDocumentForReference(documentId: string): Promise<{
    isValid: boolean;
    azureBlobUrl?: string;
    openaiFileId?: string;
    filename?: string;
    originalName?: string;
    message: string;
  }> {
    try {
      const document = await prisma.document.findUnique({
        where: { id: documentId },
      });

      if (!document) {
        return {
          isValid: false,
          message: "Document not found",
        };
      }

      if (document.isDeleted) {
        return {
          isValid: false,
          message: "Document has been deleted",
        };
      }

      if (!document.azureBlobUrl) {
        return {
          isValid: false,
          message: "Document missing Azure storage URL",
        };
      }

      return {
        isValid: true,
        azureBlobUrl: document.azureBlobUrl,
        openaiFileId: document.openaiFileId || undefined,
        filename: document.filename,
        originalName: document.originalName,
        message: "Document is valid for reference",
      };
    } catch (error) {
      logger.error(`Error validating document for reference: ${documentId}`, error);
      return {
        isValid: false,
        message: `Validation error: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  /**
   * Generate secure download URL for frontend (if needed)
   */
  async generateSecureDownloadUrl(
    documentId: string,
    expirationMinutes: number = 60
  ): Promise<{ downloadUrl?: string; expiresAt?: Date; message: string }> {
    try {
      const document = await prisma.document.findUnique({
        where: { id: documentId },
      });

      if (!document || !document.azureBlobUrl) {
        return {
          message: "Document or Azure URL not found",
        };
      }

      // For now, return the direct Azure URL
      // In production, you might want to generate a SAS token
      const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000);

      return {
        downloadUrl: document.azureBlobUrl,
        expiresAt,
        message: "Download URL generated successfully",
      };
    } catch (error) {
      logger.error(`Error generating secure download URL: ${documentId}`, error);
      return {
        message: `Failed to generate download URL: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }
}

export const documentIntegrationService = new DocumentIntegrationService();
