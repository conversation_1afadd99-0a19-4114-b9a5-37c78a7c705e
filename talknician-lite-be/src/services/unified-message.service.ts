import { prisma } from "../config/database";
import { logger } from "../utils/logger";
import { documentIntegrationService } from "./document-integration.service";
import {
  CreateUnifiedMessageData,
  OpenAIResponseEvent,
  OpenAIAnnotation,
  SourceReference,
  UnifiedMessageResponse,
} from "../types/unified-message.types";
import {
  UnifiedMessage,
  MessageAnnotation,
  MessageReference,
  $Enums,
  Prisma,
} from "@prisma/client";

// Type for UnifiedMessage with included relations
export type UnifiedMessageWithRelations = UnifiedMessage & {
  annotations: MessageAnnotation[];
  references: MessageReference[];
};

export class UnifiedMessageService {
  /**
   * Create a new unified message with annotations and references
   */
  async createUnifiedMessage(
    data: CreateUnifiedMessageData
  ): Promise<UnifiedMessageWithRelations> {
    try {
      logger.info(
        `Creating unified message for conversation: ${data.conversationId}`
      );

      const unifiedMessage = await prisma.unifiedMessage.create({
        data: {
          role: data.role as $Enums.MessageRole,
          content: data.content,
          conversationId: data.conversationId,
          userId: data.userId,
          openaiMessageId: data.openaiMessageId,
          openaiItemId: data.openaiItemId,
          sequenceNumber: data.sequenceNumber,
          metadata: data.metadata as Prisma.InputJsonValue,
          annotations: {
            create:
              data.annotations?.map(
                (
                  annotation: Omit<MessageAnnotation, "id" | "unifiedMessageId">
                ): Prisma.MessageAnnotationUncheckedCreateWithoutUnifiedMessageInput => ({
                  type: annotation.type,
                  startIndex: annotation.startIndex,
                  endIndex: annotation.endIndex,
                  url: annotation.url,
                  title: annotation.title,
                  fileId: annotation.fileId,
                  filename: annotation.filename,
                  documentId: annotation.documentId,
                  websiteId: annotation.websiteId,
                })
              ) || [],
          },
          references: {
            create:
              data.references?.map(
                (
                  reference: Omit<
                    MessageReference,
                    "id" | "createdAt" | "unifiedMessageId"
                  >
                ): Prisma.MessageReferenceUncheckedCreateWithoutUnifiedMessageInput => ({
                  type: reference.type,
                  title: reference.title,
                  documentId: reference.documentId,
                  azureBlobUrl: reference.azureBlobUrl,
                  openaiFileId: reference.openaiFileId,
                  filename: reference.filename,
                  websiteId: reference.websiteId,
                  url: reference.url,
                  scrapedContent: reference.scrapedContent,
                  metadata: reference.metadata as Prisma.InputJsonValue,
                })
              ) || [],
          },
        },
        include: {
          annotations: {
            include: {
              document: true,
              website: true,
            },
          },
          references: {
            include: {
              document: true,
              website: true,
            },
          },
          user: true,
          conversation: true,
        },
      });

      logger.info(`Created unified message: ${unifiedMessage.id}`);
      return unifiedMessage as UnifiedMessageWithRelations;
    } catch (error) {
      logger.error("Error creating unified message:", error);
      throw new Error(
        `Failed to create unified message: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Get unified messages for a conversation
   */
  async getConversationMessages(
    conversationId: string
  ): Promise<UnifiedMessageResponse[]> {
    try {
      const messages = await prisma.unifiedMessage.findMany({
        where: { conversationId },
        include: {
          annotations: {
            include: {
              document: true,
              website: true,
            },
          },
          references: {
            include: {
              document: true,
              website: true,
            },
          },
          user: true,
        },
        orderBy: { createdAt: "asc" },
      });

      return messages.map(this.transformPrismaToUnifiedMessage);
    } catch (error) {
      logger.error(
        `Error fetching conversation messages: ${conversationId}`,
        error
      );
      throw new Error(
        `Failed to fetch conversation messages: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Parse OpenAI response event and create unified message
   */
  async createFromOpenAIResponse(
    event: OpenAIResponseEvent,
    conversationId: string,
    userId: string
  ): Promise<UnifiedMessageWithRelations | null> {
    try {
      if (event.type !== "response.content_part.done" || !event.part) {
        return null;
      }

      const { part, item_id, sequence_number } = event;
      const annotations = await this.parseOpenAIAnnotations(
        part.annotations || []
      );
      const references = await this.createReferencesFromAnnotations(
        annotations
      );

      const messageData: CreateUnifiedMessageData = {
        role: "assistant",
        content: part.text,
        conversationId,
        userId,
        openaiItemId: item_id,
        sequenceNumber: sequence_number,
        annotations,
        references,
        metadata: {
          openaiEvent: event,
          logprobs: part.logprobs,
        },
      };

      return await this.createUnifiedMessage(messageData);
    } catch (error) {
      logger.error("Error creating message from OpenAI response:", error);
      throw error;
    }
  }

  /**
   * Parse OpenAI annotations and resolve document/website references
   */
  private async parseOpenAIAnnotations(
    openaiAnnotations: OpenAIAnnotation[]
  ): Promise<Omit<MessageAnnotation, "id" | "unifiedMessageId">[]> {
    const annotations: Omit<MessageAnnotation, "id" | "unifiedMessageId">[] =
      [];

    for (const annotation of openaiAnnotations) {
      const parsedAnnotation: Omit<
        MessageAnnotation,
        "id" | "unifiedMessageId"
      > = {
        type:
          annotation.type === "url_citation"
            ? $Enums.AnnotationType.URL_CITATION
            : $Enums.AnnotationType.FILE_CITATION,
        startIndex: annotation.start_index || annotation.index || 0,
        endIndex:
          annotation.end_index ||
          (annotation.start_index || annotation.index || 0) + 1,
        url: annotation.url || null,
        title: annotation.title || null,
        fileId: annotation.file_id || null,
        filename: annotation.filename || null,
        documentId: null,
        websiteId: null,
      };

      // Try to resolve document reference
      if (annotation.file_id) {
        const document = await prisma.document.findFirst({
          where: { openaiFileId: annotation.file_id },
        });
        if (document) {
          parsedAnnotation.documentId = document.id;
        }
      }

      // Try to resolve website reference by URL
      if (annotation.url) {
        const website = await prisma.scrapedWebsite.findFirst({
          where: { url: annotation.url },
        });
        if (website) {
          parsedAnnotation.websiteId = website.id;
        }
      }

      annotations.push(parsedAnnotation);
    }

    return annotations;
  }

  /**
   * Create message references from annotations
   */
  private async createReferencesFromAnnotations(
    annotations: Omit<MessageAnnotation, "id" | "unifiedMessageId">[]
  ): Promise<
    Omit<MessageReference, "id" | "createdAt" | "unifiedMessageId">[]
  > {
    const references: Omit<
      MessageReference,
      "id" | "createdAt" | "unifiedMessageId"
    >[] = [];
    const processedRefs = new Set<string>();

    for (const annotation of annotations) {
      // Create document reference with validation
      if (
        annotation.documentId &&
        !processedRefs.has(`doc-${annotation.documentId}`)
      ) {
        const validation =
          await documentIntegrationService.validateDocumentForReference(
            annotation.documentId
          );

        if (validation.isValid) {
          references.push({
            type: $Enums.ReferenceType.DOCUMENT,
            title:
              annotation.filename ||
              validation.originalName ||
              "Unknown Document",
            documentId: annotation.documentId,
            azureBlobUrl: validation.azureBlobUrl || null,
            openaiFileId: validation.openaiFileId || null,
            filename: validation.filename || null,
            url: null,
            websiteId: null,
            scrapedContent: null,
            metadata: {
              hasAzureUrl: !!validation.azureBlobUrl,
              hasOpenAIFile: !!validation.openaiFileId,
              isComplete:
                !!validation.azureBlobUrl && !!validation.openaiFileId,
            },
          });
          processedRefs.add(`doc-${annotation.documentId}`);
        } else {
          logger.warn(`Invalid document reference: ${annotation.documentId}`, {
            reason: validation.message,
          });
        }
      }

      // Create website reference
      if (
        annotation.websiteId &&
        !processedRefs.has(`web-${annotation.websiteId}`)
      ) {
        const website = await prisma.scrapedWebsite.findUnique({
          where: { id: annotation.websiteId },
        });
        if (website) {
          references.push({
            type: $Enums.ReferenceType.WEBSITE,
            title: annotation.title || website.title,
            websiteId: website.id,
            url: website.url,
            scrapedContent: website.content,
            metadata: (website.metadata as Record<string, any>) || {},
            documentId: null,
            azureBlobUrl: null,
            openaiFileId: null,
            filename: null,
          });
          processedRefs.add(`web-${annotation.websiteId}`);
        }
      }

      // Create URL reference for external links
      if (
        annotation.url &&
        !annotation.websiteId &&
        !processedRefs.has(`url-${annotation.url}`)
      ) {
        references.push({
          type: $Enums.ReferenceType.WEBSITE,
          title: annotation.title || annotation.url,
          url: annotation.url,
          metadata: { external: true },
          documentId: null,
          azureBlobUrl: null,
          openaiFileId: null,
          filename: null,
          websiteId: null,
          scrapedContent: null,
        });
        processedRefs.add(`url-${annotation.url}`);
      }
    }

    return references;
  }

  /**
   * Transform Prisma result to UnifiedMessage type
   */
  private transformPrismaToUnifiedMessage(
    prismaMessage: UnifiedMessageWithRelations
  ): UnifiedMessageResponse {
    return {
      id: prismaMessage.id,
      role: prismaMessage.role.toLowerCase(),
      content: prismaMessage.content,
      conversationId: prismaMessage.conversationId,
      userId: prismaMessage.userId,
      createdAt: prismaMessage.createdAt.toISOString(),
      updatedAt: prismaMessage.updatedAt.toISOString(),
      openaiMessageId: prismaMessage.openaiMessageId || undefined,
      openaiItemId: prismaMessage.openaiItemId || undefined,
      sequenceNumber: prismaMessage.sequenceNumber || undefined,
      metadata: prismaMessage.metadata,
      annotations:
        prismaMessage.annotations?.map((ann: MessageAnnotation) => ({
          id: ann.id,
          type: ann.type.toLowerCase() as "url_citation" | "file_citation",
          startIndex: ann.startIndex,
          endIndex: ann.endIndex,
          url: ann.url || undefined,
          title: ann.title || undefined,
          fileId: ann.fileId || undefined,
          filename: ann.filename || undefined,
          documentId: ann.documentId || undefined,
          websiteId: ann.websiteId || undefined,
        })) || [],
      references:
        prismaMessage.references?.map((ref: MessageReference) => ({
          id: ref.id,
          type: ref.type.toLowerCase() as "document" | "website",
          title: ref.title,
          documentId: ref.documentId || undefined,
          azureBlobUrl: ref.azureBlobUrl || undefined,
          openaiFileId: ref.openaiFileId || undefined,
          filename: ref.filename || undefined,
          websiteId: ref.websiteId || undefined,
          url: ref.url || undefined,
          scrapedContent: ref.scrapedContent || undefined,
          metadata: ref.metadata,
          createdAt: ref.createdAt.toISOString(),
        })) || [],
    };
  }

  /**
   * Convert UnifiedMessage to frontend-compatible format
   */
  convertToSourceReferences(
    message: UnifiedMessageWithRelations
  ): SourceReference[] {
    return message.references.map((ref: MessageReference) => ({
      type: ref.type.toLowerCase() as "document" | "website",
      title: ref.title,
      content: ref.scrapedContent || undefined,
      url: ref.url || undefined,
      documentId: ref.documentId || undefined,
      websiteId: ref.websiteId || undefined,
      azureBlobUrl: ref.azureBlobUrl || undefined,
      filename: ref.filename || undefined,
    }));
  }
}

export const unifiedMessageService = new UnifiedMessageService();
