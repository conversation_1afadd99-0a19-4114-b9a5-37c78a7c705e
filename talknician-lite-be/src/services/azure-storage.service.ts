import {
  BlobServiceClient,
  StorageSharedKeyCredential,
  ContainerClient,
} from "@azure/storage-blob";
import { logger } from "../utils/logger";
import { config } from "../config";
import fs from "fs";
import path from "path";

export class AzureStorageService {
  private blobServiceClient: BlobServiceClient;
  private containerClient: ContainerClient;
  private containerName: string;

  constructor() {
    this.containerName = config.azure.storage.containerName;

    // Create shared key credential
    const sharedKeyCredential = new StorageSharedKeyCredential(
      config.azure.storage.accountName,
      config.azure.storage.accountKey
    );

    // Create blob service client
    this.blobServiceClient = new BlobServiceClient(
      `https://${config.azure.storage.accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    // Get container client
    this.containerClient = this.blobServiceClient.getContainerClient(
      this.containerName
    );
  }

  /**
   * Initialize the container (create if it doesn't exist)
   */
  async initializeContainer(): Promise<void> {
    try {
      await this.containerClient.createIfNotExists({
        access: "blob", // Allow public read access to blobs
      });
      logger.info("Azure container initialized", {
        containerName: this.containerName,
      });
    } catch (error) {
      logger.error("Error initializing Azure container:", error);
      throw new Error("Failed to initialize Azure Storage container");
    }
  }

  /**
   * Upload file to Azure Blob Storage
   */
  async uploadFile(
    file: Express.Multer.File,
    organizationId: string
  ): Promise<string> {
    try {
      // Ensure container exists
      await this.initializeContainer();

      // Generate unique blob name with organization prefix
      const timestamp = Date.now();
      const randomSuffix = Math.round(Math.random() * 1e9);
      const fileExtension = path.extname(file.originalname);
      const blobName = `${organizationId}/${timestamp}-${randomSuffix}${fileExtension}`;

      // Get block blob client
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      // Upload file
      const uploadResponse = await blockBlobClient.uploadFile(file.path, {
        blobHTTPHeaders: {
          blobContentType: file.mimetype,
        },
        metadata: {
          originalName: file.originalname,
          organizationId: organizationId,
          uploadedAt: new Date().toISOString(),
        },
      });

      logger.info("File uploaded to Azure Storage", {
        blobName,
        organizationId,
        filename: file.originalname,
        requestId: uploadResponse.requestId,
      });

      // Clean up local file after successful upload
      try {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
          logger.debug("Local file cleaned up", { localPath: file.path });
        }
      } catch (cleanupError) {
        logger.warn("Failed to clean up local file:", cleanupError);
      }

      return blockBlobClient.url;
    } catch (error) {
      logger.error("Error uploading to Azure:", error);
      throw new Error("Failed to upload to Azure Storage");
    }
  }

  /**
   * Download file from Azure Blob Storage
   */
  async downloadFile(blobUrl: string): Promise<Buffer> {
    try {
      const blobName = this.extractBlobNameFromUrl(blobUrl);
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      // Download blob to buffer
      const downloadResponse = await blockBlobClient.download();

      if (!downloadResponse.readableStreamBody) {
        throw new Error("No readable stream in download response");
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      for await (const chunk of downloadResponse.readableStreamBody) {
        chunks.push(chunk instanceof Buffer ? chunk : Buffer.from(chunk));
      }

      const buffer = Buffer.concat(chunks);

      logger.info("File downloaded from Azure Storage", {
        blobName,
        size: buffer.length,
      });

      return buffer;
    } catch (error) {
      logger.error("Error downloading from Azure:", error);
      throw new Error("Failed to download from Azure Storage");
    }
  }

  /**
   * Get a readable stream for a file from Azure Blob Storage
   */
  async getFileStream(blobUrl: string): Promise<NodeJS.ReadableStream> {
    try {
      const blobName = this.extractBlobNameFromUrl(blobUrl);
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      const downloadResponse = await blockBlobClient.download();

      if (!downloadResponse.readableStreamBody) {
        throw new Error("No readable stream in download response");
      }

      logger.info("File stream created from Azure Storage", { blobName });

      return downloadResponse.readableStreamBody;
    } catch (error) {
      logger.error("Error creating file stream from Azure:", error);
      throw new Error("Failed to create file stream from Azure Storage");
    }
  }

  /**
   * Delete file from Azure Blob Storage
   */
  async deleteFile(blobUrl: string): Promise<void> {
    try {
      const blobName = this.extractBlobNameFromUrl(blobUrl);
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      await blockBlobClient.delete();

      logger.info("File deleted from Azure Storage", { blobName });
    } catch (error) {
      logger.error("Error deleting from Azure:", error);
      throw new Error("Failed to delete from Azure Storage");
    }
  }

  /**
   * Check if a file exists in Azure Blob Storage
   */
  async fileExists(blobUrl: string): Promise<boolean> {
    try {
      const blobName = this.extractBlobNameFromUrl(blobUrl);
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      return await blockBlobClient.exists();
    } catch (error) {
      logger.error("Error checking file existence in Azure:", error);
      return false;
    }
  }

  private extractBlobNameFromUrl(blobUrl: string): string {
    const url = new URL(blobUrl);
    // Remove the container name and leading slash from the pathname
    const pathParts = url.pathname.split("/");
    // Skip the first empty part and the container name
    return pathParts.slice(2).join("/");
  }
}

export const azureStorageService = new AzureStorageService();
