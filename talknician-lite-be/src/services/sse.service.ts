import SSE from "express-sse";
import { logger } from "../utils/logger";

interface ChatSSEData {
  type: "connected" | "content" | "status" | "completed" | "error" | "end";
  data?: any;
  conversationId?: string;
  messageId?: string;
}

interface SSEConnection {
  sse: SSE;
  conversationId: string;
  userId: string;
  createdAt: Date;
}

class SSEService {
  private connections: Map<string, SSEConnection> = new Map();

  /**
   * Create a new SSE connection for a conversation
   */
  createConnection(conversationId: string, userId: string): SSE {
    const connectionId = `${conversationId}-${userId}`;

    // Close existing connection if any
    this.closeConnection(connectionId);

    // Create new SSE instance with initial connection event
    const sse = new SSE(
      [
        {
          type: "connected",
          data: {
            conversationId,
            userId,
            timestamp: new Date().toISOString(),
          },
        },
      ],
      {
        isSerialized: false,
        initialEvent: "connection",
      }
    );

    // Store connection
    this.connections.set(connectionId, {
      sse,
      conversationId,
      userId,
      createdAt: new Date(),
    });

    logger.info(
      `SSE connection created for conversation ${conversationId}, user ${userId}`
    );
    return sse;
  }

  /**
   * Send data to a specific conversation's SSE connection
   */
  sendToConversation(
    conversationId: string,
    userId: string,
    data: ChatSSEData
  ): boolean {
    const connectionId = `${conversationId}-${userId}`;
    const connection = this.connections.get(connectionId);

    if (!connection) {
      logger.warn(
        `No SSE connection found for conversation ${conversationId}, user ${userId}`
      );
      return false;
    }

    try {
      // Send with event type based on data.type
      connection.sse.send(data, data.type);
      return true;
    } catch (error) {
      logger.error(`Error sending SSE data to ${connectionId}:`, error);
      this.closeConnection(connectionId);
      return false;
    }
  }

  /**
   * Send content chunk to conversation
   */
  sendContent(
    conversationId: string,
    userId: string,
    content: string,
    messageId?: string
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "content",
      data: { content },
      conversationId,
      messageId,
    });
  }

  /**
   * Send status update to conversation
   */
  sendStatus(
    conversationId: string,
    userId: string,
    status: string,
    details?: any
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "status",
      data: { status, ...details },
      conversationId,
    });
  }

  /**
   * Send completion event to conversation
   */
  sendCompletion(
    conversationId: string,
    userId: string,
    messageId: string,
    sources?: any[]
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "completed",
      data: { messageId, sources },
      conversationId,
      messageId,
    });
  }

  /**
   * Send error to conversation
   */
  sendError(
    conversationId: string,
    userId: string,
    error: string,
    details?: any
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "error",
      data: { error, ...details },
      conversationId,
    });
  }

  /**
   * Close a specific SSE connection
   */
  closeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      try {
        // express-sse doesn't have explicit close method, but we can remove from our tracking
        this.connections.delete(connectionId);
        logger.info(`SSE connection closed: ${connectionId}`);
      } catch (error) {
        logger.error(`Error closing SSE connection ${connectionId}:`, error);
      }
    }
  }

  /**
   * Close all connections for a user
   */
  closeUserConnections(userId: string): void {
    const userConnections = Array.from(this.connections.entries()).filter(
      ([_, connection]) => connection.userId === userId
    );

    userConnections.forEach(([connectionId]) => {
      this.closeConnection(connectionId);
    });

    logger.info(
      `Closed ${userConnections.length} SSE connections for user ${userId}`
    );
  }

  /**
   * Get active connection count
   */
  getActiveConnectionCount(): number {
    return this.connections.size;
  }

  /**
   * Get connections for a specific conversation
   */
  getConversationConnections(conversationId: string): SSEConnection[] {
    return Array.from(this.connections.values()).filter(
      (connection) => connection.conversationId === conversationId
    );
  }

  /**
   * Cleanup old connections (older than 1 hour)
   */
  cleanupOldConnections(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const oldConnections = Array.from(this.connections.entries()).filter(
      ([_, connection]) => connection.createdAt < oneHourAgo
    );

    oldConnections.forEach(([connectionId]) => {
      this.closeConnection(connectionId);
    });

    if (oldConnections.length > 0) {
      logger.info(`Cleaned up ${oldConnections.length} old SSE connections`);
    }
  }
}

// Export singleton instance
export const sseService = new SSEService();

// Cleanup old connections every 30 minutes
setInterval(() => {
  sseService.cleanupOldConnections();
}, 30 * 60 * 1000);
