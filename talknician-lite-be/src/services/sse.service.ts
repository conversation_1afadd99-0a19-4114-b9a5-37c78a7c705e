import { Response } from "express";
import { logger } from "../utils/logger";

interface ChatSSEData {
  type:
    | "connected"
    | "content"
    | "tool_calls_started"
    | "completed"
    | "error"
    | "end";
  content?: string;
  data?: any;
  conversationId?: string;
  messageId?: string;
  status?: string;
}

interface SSEConnection {
  res: Response;
  conversationId: string;
  userId: string;
  createdAt: Date;
  isActive: boolean;
}

class SSEService {
  private connections: Map<string, SSEConnection> = new Map();

  /**
   * Create a new SSE connection for a conversation
   */
  createConnection(
    conversationId: string,
    userId: string,
    res: Response
  ): void {
    const connectionId = `${conversationId}-${userId}`;

    // Close existing connection if any
    this.closeConnection(connectionId);

    // Set SSE headers
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
    });

    // Store connection
    this.connections.set(connectionId, {
      res,
      conversationId,
      userId,
      createdAt: new Date(),
      isActive: true,
    });

    // Send initial connection event
    this.sendEvent(res, "connected", {
      type: "connected",
      data: {
        conversationId,
        userId,
        timestamp: new Date().toISOString(),
      },
    });

    // Handle client disconnect
    res.on("close", () => {
      this.closeConnection(connectionId);
    });

    logger.info(
      `SSE connection created for conversation ${conversationId}, user ${userId}`
    );
  }

  /**
   * Send a properly formatted SSE event
   */
  private sendEvent(res: Response, event: string, data: any): boolean {
    try {
      if (res.destroyed || res.writableEnded) {
        return false;
      }

      const eventData = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
      res.write(eventData);
      return true;
    } catch (error) {
      logger.error("Error sending SSE event:", error);
      return false;
    }
  }

  /**
   * Send data to a specific conversation's SSE connection
   */
  sendToConversation(
    conversationId: string,
    userId: string,
    data: ChatSSEData
  ): boolean {
    const connectionId = `${conversationId}-${userId}`;
    const connection = this.connections.get(connectionId);

    if (!connection || !connection.isActive) {
      logger.warn(
        `No active SSE connection found for conversation ${conversationId}, user ${userId}`
      );
      return false;
    }

    return this.sendEvent(connection.res, data.type, data);
  }

  /**
   * Send content chunk to conversation
   */
  sendContent(
    conversationId: string,
    userId: string,
    content: string,
    messageId?: string
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "content",
      content,
      conversationId,
      messageId,
    });
  }

  /**
   * Send tool calls started status to conversation
   */
  sendToolCallsStarted(
    conversationId: string,
    userId: string,
    status: string,
    details?: any
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "tool_calls_started",
      content: status,
      status,
      conversationId,
      ...details,
    });
  }

  /**
   * Send completion event to conversation
   */
  sendCompletion(
    conversationId: string,
    userId: string,
    messageData: any
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "completed",
      data: messageData,
      conversationId,
    });
  }

  /**
   * Send error to conversation
   */
  sendError(
    conversationId: string,
    userId: string,
    error: string,
    details?: any
  ): boolean {
    return this.sendToConversation(conversationId, userId, {
      type: "error",
      content: error,
      data: { error, ...details },
      conversationId,
    });
  }

  /**
   * Send end event to conversation
   */
  sendEnd(conversationId: string, userId: string): boolean {
    const result = this.sendToConversation(conversationId, userId, {
      type: "end",
      conversationId,
    });

    // Close connection after sending end event
    this.closeConnection(`${conversationId}-${userId}`);
    return result;
  }

  /**
   * Close a specific SSE connection
   */
  closeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      try {
        connection.isActive = false;
        if (!connection.res.destroyed && !connection.res.writableEnded) {
          connection.res.end();
        }
        this.connections.delete(connectionId);
        logger.info(`SSE connection closed: ${connectionId}`);
      } catch (error) {
        logger.error(`Error closing SSE connection ${connectionId}:`, error);
      }
    }
  }

  /**
   * Close all connections for a user
   */
  closeUserConnections(userId: string): void {
    const userConnections = Array.from(this.connections.entries()).filter(
      ([_, connection]) => connection.userId === userId
    );

    userConnections.forEach(([connectionId]) => {
      this.closeConnection(connectionId);
    });

    logger.info(
      `Closed ${userConnections.length} SSE connections for user ${userId}`
    );
  }

  /**
   * Get active connection count
   */
  getActiveConnectionCount(): number {
    return Array.from(this.connections.values()).filter(
      (connection) => connection.isActive
    ).length;
  }

  /**
   * Get connections for a specific conversation
   */
  getConversationConnections(conversationId: string): SSEConnection[] {
    return Array.from(this.connections.values()).filter(
      (connection) =>
        connection.conversationId === conversationId && connection.isActive
    );
  }

  /**
   * Cleanup old connections (older than 1 hour)
   */
  cleanupOldConnections(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const oldConnections = Array.from(this.connections.entries()).filter(
      ([_, connection]) => connection.createdAt < oneHourAgo
    );

    oldConnections.forEach(([connectionId]) => {
      this.closeConnection(connectionId);
    });

    if (oldConnections.length > 0) {
      logger.info(`Cleaned up ${oldConnections.length} old SSE connections`);
    }
  }
}

// Export singleton instance
export const sseService = new SSEService();

// Cleanup old connections every 30 minutes
setInterval(() => {
  sseService.cleanupOldConnections();
}, 30 * 60 * 1000);
