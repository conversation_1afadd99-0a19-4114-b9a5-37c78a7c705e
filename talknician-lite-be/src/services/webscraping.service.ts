import OpenAI from "openai";
import { logger } from "../utils/logger";
import { prisma } from "../config/database";
import { azureStorageService } from "./azure-storage.service";
import fs from "fs";
import path from "path";

export interface ScrapedContent {
  url: string;
  title: string;
  content: string;
  markdown: string;
  metadata: {
    description?: string;
    keywords?: string[];
    author?: string;
    publishedDate?: string;
    wordCount?: number;
    language?: string;
  };
}

export class WebScrapingService {
  private openai: OpenAI;
  private crawl4aiUrl =
    process.env.CRAWL4AI_SERVICE_URL || "http://localhost:8001";

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Scrape a website using Crawl4AI service
   */
  async scrapeWebsite(url: string): Promise<ScrapedContent> {
    try {
      logger.info(`Scraping website with Crawl4AI: ${url}`);

      const response = await fetch(`${this.crawl4aiUrl}/scrape`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url,
          extract_main_content: true,
          include_links: false,
          word_count_threshold: 10,
        }),
      });

      if (!response.ok) {
        throw new Error(`Crawl4AI service error: ${response.status}`);
      }

      const data = await response.json();

      const responseData = data as any;
      if (!responseData.success) {
        throw new Error(responseData.error || "Failed to scrape website");
      }

      return {
        url: responseData.url,
        title: responseData.title || url,
        content: responseData.content,
        markdown: responseData.markdown,
        metadata: {
          description: responseData.metadata?.description,
          keywords: responseData.metadata?.keywords,
          author: responseData.metadata?.author,
          publishedDate: responseData.metadata?.published_date,
          wordCount: responseData.metadata?.word_count,
          language: responseData.metadata?.language,
        },
      };
    } catch (error) {
      logger.error(`Error scraping website ${url}:`, error);
      throw new Error(
        `Failed to scrape website: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Validate if a URL is accessible and scrapable using Crawl4AI service
   */
  async validateUrl(url: string): Promise<{
    accessible: boolean;
    canScrape: boolean;
    title?: string;
    error?: string;
  }> {
    try {
      logger.info(`Validating URL with Crawl4AI: ${url}`);

      const response = await fetch(`${this.crawl4aiUrl}/validate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url,
          word_count_threshold: 1,
        }),
      });

      if (!response.ok) {
        throw new Error(`Crawl4AI service error: ${response.status}`);
      }

      const data = await response.json();

      const responseData = data as any;
      return {
        accessible: responseData.accessible,
        canScrape: responseData.can_scrape,
        title: responseData.title,
        error: responseData.error,
      };
    } catch (error) {
      logger.error(`Error validating URL ${url}:`, error);
      return {
        accessible: false,
        canScrape: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Extract text content from HTML using simple regex patterns (fallback method)
   */
  private extractTextContent(html: string): string {
    // Remove script and style elements
    let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "");
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "");

    // Remove HTML tags
    text = text.replace(/<[^>]*>/g, " ");

    // Decode HTML entities
    text = text.replace(/&nbsp;/g, " ");
    text = text.replace(/&amp;/g, "&");
    text = text.replace(/&lt;/g, "<");
    text = text.replace(/&gt;/g, ">");
    text = text.replace(/&quot;/g, '"');
    text = text.replace(/&#39;/g, "'");

    // Clean up whitespace
    text = text.replace(/\s+/g, " ");
    text = text.trim();

    return text;
  }

  /**
   * Extract title from HTML
   */
  private extractTitle(html: string): string | null {
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    if (titleMatch) {
      return titleMatch[1].trim();
    }
    return null;
  }

  /**
   * Add scraped website to RAG system
   */
  async addWebsiteToRAG(
    url: string,
    organizationId: string,
    userId: string
  ): Promise<{ success: boolean; documentId?: string; message: string }> {
    try {
      logger.info(
        `Adding website to RAG: ${url} for organization: ${organizationId}`
      );

      // Check if URL already exists by checking filename pattern
      const urlHostname = new URL(url).hostname;
      const existingDoc = await prisma.document.findFirst({
        where: {
          organizationId,
          filename: {
            contains: `website_${urlHostname}`,
          },
        },
      });

      if (existingDoc) {
        return {
          success: false,
          message: "Website already exists in the knowledge base",
        };
      }

      // Scrape the website
      const scrapedContent = await this.scrapeWebsite(url);

      // Create a text file with the scraped content
      const contentHostname = new URL(scrapedContent.url).hostname;
      const fileName = `website_${contentHostname}_${Date.now()}.txt`;
      const fileContent = `Title: ${scrapedContent.title}\nURL: ${scrapedContent.url}\n\n${scrapedContent.content}`;

      // Create temporary file for Azure upload
      const tempDir = "temp";
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempPath = path.join(tempDir, fileName);
      fs.writeFileSync(tempPath, fileContent, "utf-8");

      // Create file object for Azure upload
      const tempFile = {
        path: tempPath,
        originalname: fileName,
        mimetype: "text/plain",
        size: Buffer.byteLength(fileContent, "utf-8"),
      } as Express.Multer.File;

      // Upload to Azure Storage
      const azureUrl = await azureStorageService.uploadFile(
        tempFile,
        organizationId
      );

      // Create document record
      const document = await prisma.document.create({
        data: {
          filename: fileName,
          originalName: scrapedContent.title,
          size: Buffer.byteLength(fileContent, "utf-8"),
          mimeType: "text/plain",
          azureBlobUrl: azureUrl,
          organizationId,
          userId,
          uploadedBy: userId,
          status: "PROCESSING",
        },
      });

      // Get organization to check for vector store
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization?.vectorStoreId) {
        // Create vector store if it doesn't exist
        const vectorStore = await this.openai.vectorStores.create({
          name: `${organization?.name || "Organization"} Knowledge Base`,
          expires_after: {
            anchor: "last_active_at",
            days: 365,
          },
        });

        await prisma.organization.update({
          where: { id: organizationId },
          data: { vectorStoreId: vectorStore.id },
        });

        organization!.vectorStoreId = vectorStore.id;
      }

      // Create a File object for OpenAI upload
      const fileForOpenAI = new File([fileContent], fileName, {
        type: "text/plain",
      });

      // Upload file to OpenAI
      const openaiFile = await this.openai.files.create({
        file: fileForOpenAI,
        purpose: "assistants",
      });

      // Add file to vector store
      await this.openai.vectorStores.files.create(organization!.vectorStoreId, {
        file_id: openaiFile.id,
      });

      // Update document with OpenAI file ID and mark as completed
      await prisma.document.update({
        where: { id: document.id },
        data: {
          openaiFileId: openaiFile.id,
          status: "COMPLETED",
        },
      });

      logger.info(`Successfully added website to RAG: ${url}`);

      return {
        success: true,
        documentId: document.id,
        message: "Website successfully added to knowledge base",
      };
    } catch (error) {
      logger.error(`Error adding website to RAG:`, error);
      return {
        success: false,
        message: `Failed to add website: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  /**
   * Validate URL format
   */
  isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === "http:" || urlObj.protocol === "https:";
    } catch {
      return false;
    }
  }
}

export const webScrapingService = new WebScrapingService();
