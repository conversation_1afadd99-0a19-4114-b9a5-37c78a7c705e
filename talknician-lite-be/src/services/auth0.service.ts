import axios from "axios";
import jwt from "jsonwebtoken";
import { config } from "@/config";
import { logger } from "@/utils/logger";

export interface Auth0User {
  user_id: string;
  email: string;
  name?: string;
  picture?: string;
  email_verified: boolean;
}

export interface Auth0LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope?: string;
}

export interface Auth0SignupData {
  email: string;
  password: string;
  name?: string;
  connection?: string;
}

export interface ManagementApiToken {
  access_token: string;
  expires_in: number;
  token_type: string;
}

class Auth0Service {
  private managementToken: string | null = null;
  private managementTokenExpiry: number = 0;
  private jwksCache: Map<string, string> = new Map();
  private jwksCacheExpiry: number = 0;

  constructor() {
    // JWKS cache will be populated on first use
  }

  /**
   * Custom login using Resource Owner Password Grant
   * This allows us to handle login in our custom UI instead of redirecting to Auth0
   */
  async login(email: string, password: string): Promise<Auth0LoginResponse> {
    try {
      const response = await axios.post(
        `https://${config.auth0.domain}/oauth/token`,
        {
          grant_type: "password",
          username: email,
          password,
          client_id: config.auth0.clientId,
          client_secret: config.auth0.clientSecret,
          audience: config.auth0.audience,
          scope: "openid profile email",
        }
      );

      logger.info("Auth0 login successful", { email });
      return response.data;
    } catch (error: any) {
      logger.error("Auth0 login failed", {
        email,
        error: error.response?.data || error.message,
      });

      if (error.response?.status === 403) {
        throw new Error("Invalid credentials");
      }
      if (error.response?.status === 429) {
        throw new Error("Too many login attempts. Please try again later.");
      }

      throw new Error("Login failed. Please try again.");
    }
  }

  /**
   * Create user in Auth0 using Management API
   */
  async signupUser(userData: Auth0SignupData): Promise<Auth0User> {
    try {
      const managementToken = await this.getManagementApiToken();

      const response = await axios.post(
        `https://${config.auth0.domain}/api/v2/users`,
        {
          email: userData.email,
          password: userData.password,
          name: userData.name || userData.email.split("@")[0],
          connection: userData.connection || "Username-Password-Authentication",
          email_verified: false,
        },
        {
          headers: {
            Authorization: `Bearer ${managementToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      logger.info("Auth0 user created successfully", { email: userData.email });
      return response.data;
    } catch (error: any) {
      logger.error("Auth0 signup failed", {
        email: userData.email,
        error: error.response?.data || error.message,
      });

      if (error.response?.status === 409) {
        throw new Error("User already exists");
      }

      throw new Error("Signup failed. Please try again.");
    }
  }

  /**
   * Get user profile from Auth0
   */
  async getUserProfile(accessToken: string): Promise<Auth0User> {
    try {
      const response = await axios.get(
        `https://${config.auth0.domain}/userinfo`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      const userInfo = response.data;

      // Map the userinfo response to our Auth0User interface
      return {
        user_id: userInfo.sub, // userinfo returns 'sub' instead of 'user_id'
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        email_verified: userInfo.email_verified || false,
      };
    } catch (error: any) {
      logger.error("Failed to get user profile", { error: error.message });
      throw new Error("Failed to get user profile");
    }
  }

  /**
   * Get JWKS signing key for token verification
   */
  private async getSigningKey(kid: string): Promise<string> {
    // Check cache first (cache for 24 hours)
    if (this.jwksCache.has(kid) && Date.now() < this.jwksCacheExpiry) {
      return this.jwksCache.get(kid)!;
    }

    try {
      const response = await axios.get(config.auth0.jwksUri, {
        timeout: 10000,
      });

      const jwks = response.data;
      const key = jwks.keys.find((k: any) => k.kid === kid);

      if (!key) {
        throw new Error(`Unable to find a signing key that matches '${kid}'`);
      }

      // Convert JWK to PEM format
      const cert = key.x5c[0];
      const publicKey = `-----BEGIN CERTIFICATE-----\n${cert}\n-----END CERTIFICATE-----`;

      // Cache the key for 24 hours
      this.jwksCache.set(kid, publicKey);
      this.jwksCacheExpiry = Date.now() + 24 * 60 * 60 * 1000;

      return publicKey;
    } catch (error: any) {
      logger.error("Failed to get JWKS signing key", {
        error: error.message,
        kid,
        jwksUri: config.auth0.jwksUri,
      });
      throw new Error("Failed to get signing key");
    }
  }

  /**
   * Verify JWT token from Auth0
   */
  async verifyToken(token: string): Promise<any> {
    try {
      // Get the JWT header to find the key ID
      const decoded = jwt.decode(token, { complete: true });
      if (!decoded || !decoded.header.kid) {
        logger.error("Token decode failed", {
          hasDecoded: !!decoded,
          hasKid: !!decoded?.header?.kid,
        });
        throw new Error("Invalid token");
      }

      // Log token details for debugging
      const tokenPayload = decoded.payload as any;
      logger.info("Token verification attempt", {
        sub: tokenPayload.sub,
        aud: tokenPayload.aud,
        iss: tokenPayload.iss,
        exp: tokenPayload.exp,
        configAudience: config.auth0.audience,
        configIssuer: config.auth0.issuer,
      });

      // Get the signing key from Auth0 JWKS
      const signingKey = await this.getSigningKey(decoded.header.kid);

      // Handle multiple audiences - check if our audience is in the array
      const tokenAudience = tokenPayload.aud;
      const expectedAudience = config.auth0.audience;

      let audienceValid = false;
      if (Array.isArray(tokenAudience)) {
        audienceValid = tokenAudience.includes(expectedAudience);
      } else {
        audienceValid = tokenAudience === expectedAudience;
      }

      if (!audienceValid) {
        logger.error("Audience mismatch", {
          tokenAudience,
          expectedAudience,
          tokenIsArray: Array.isArray(tokenAudience),
        });
        throw new Error("Invalid audience");
      }

      // Verify the token
      const verifiedPayload = jwt.verify(token, signingKey, {
        issuer: config.auth0.issuer,
        algorithms: [config.auth0.algorithm],
        // We handle audience validation manually above
      });

      logger.info("Token verification successful", {
        sub: (verifiedPayload as any)?.sub,
        email: (verifiedPayload as any)?.email || "no-email",
      });

      return verifiedPayload;
    } catch (error: any) {
      logger.error("Token verification failed", {
        error: error.message,
        errorName: error.name,
      });
      throw new Error("Token verification failed");
    }
  }

  /**
   * Get Management API token for Auth0 operations
   */
  private async getManagementApiToken(): Promise<string> {
    // Return cached token if still valid
    if (this.managementToken && Date.now() < this.managementTokenExpiry) {
      return this.managementToken;
    }

    if (!config.management.clientId || !config.management.clientSecret) {
      throw new Error("Management API credentials not configured");
    }

    try {
      const response = await axios.post(
        `https://${config.auth0.domain}/oauth/token`,
        {
          grant_type: "client_credentials",
          client_id: config.management.clientId,
          client_secret: config.management.clientSecret,
          audience: `https://${config.auth0.domain}/api/v2/`,
        }
      );

      const data: ManagementApiToken = response.data;
      this.managementToken = data.access_token;
      this.managementTokenExpiry = Date.now() + data.expires_in * 1000 - 60000; // 1 minute buffer

      return this.managementToken;
    } catch (error: any) {
      logger.error("Failed to get Management API token", {
        error: error.message,
      });
      throw new Error("Failed to authenticate with Auth0 Management API");
    }
  }

  /**
   * Send verification email
   */
  async sendVerificationEmail(userId: string): Promise<void> {
    try {
      const managementToken = await this.getManagementApiToken();

      await axios.post(
        `https://${config.auth0.domain}/api/v2/jobs/verification-email`,
        {
          user_id: userId,
        },
        {
          headers: {
            Authorization: `Bearer ${managementToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      logger.info("Verification email sent", { userId });
    } catch (error: any) {
      logger.error("Failed to send verification email", {
        userId,
        error: error.response?.data || error.message,
      });
      throw new Error("Failed to send verification email");
    }
  }

  /**
   * Reset password
   */
  async resetPassword(email: string): Promise<void> {
    try {
      await axios.post(
        `https://${config.auth0.domain}/dbconnections/change_password`,
        {
          client_id: config.auth0.clientId,
          email,
          connection: "Username-Password-Authentication",
        }
      );

      logger.info("Password reset email sent", { email });
    } catch (error: any) {
      logger.error("Failed to send password reset email", {
        email,
        error: error.response?.data || error.message,
      });
      throw new Error("Failed to send password reset email");
    }
  }
}

export const auth0Service = new Auth0Service();
export default auth0Service;
