import OpenAI from "openai";
import { MessageRole, PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import {
  ResponseCreateParamsNonStreaming,
  ResponseCreateParamsStreaming,
  ResponseInput,
  ResponseInputItem,
} from "openai/resources/responses/responses";
import { OpenAIResponseEvent } from "@/types/unified-message.types";
import {
  unifiedMessageService,
  UnifiedMessageWithRelations,
} from "./unified-message.service";
import { sseService } from "./sse.service";

const prisma = new PrismaClient();

interface ChatSettings {
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
  documentSearch?: boolean;
}

interface SourceReference {
  type: "document" | "web" | "general";
  title: string;
  content?: string;
  url?: string;
  documentId?: string;
}

export class ChatService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async sendMessage(
    conversationId: string,
    userInput: string,
    userId: string,
    organizationId: string,
    settings: ChatSettings = {},
    sse: SSE
  ): Promise<UnifiedMessageWithRelations | null> {
    let finalUnifiedMessage = null;
    const messages = await prisma.unifiedMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: "asc" },
    });

    //convert messages to OpenAI format
    const formattedMessages: ResponseInputItem[] = messages.map((message) => {
      return {
        role: message.role as MessageRole,
        content: message.content,
      };
    });

    //Document search
    let vector_store_id = null;
    if (settings.documentSearch) {
      const organization = await prisma.organization.findFirst({
        where: { id: organizationId },
        select: { vectorStoreId: true },
      });
      vector_store_id = organization?.vectorStoreId;
    }
    const tools: ResponseCreateParamsNonStreaming["tools"] = [];
    if (vector_store_id) {
      tools.push({ type: "file_search", vector_store_ids: [vector_store_id] });
    }

    //Web search
    if (settings.enableWebSearch) {
      tools.push({ type: "web_search_preview" });
    }

    //Personality
    if (settings.personality) {
      formattedMessages.unshift({
        role: MessageRole.developer,
        content: settings.personality,
      });
    }

    //Save user message to UnifiedMessage
    await unifiedMessageService.createUnifiedMessage({
      role: MessageRole.user,
      content: userInput,
      conversationId,
      userId,
    });

    // Test UnifiedMessage system with file search
    console.log("=== Testing UnifiedMessage with File Search ===");

    const openAIOptions: ResponseCreateParamsStreaming = {
      model: "gpt-4.1",
      instructions:
        "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
      input: [
        ...formattedMessages,
        { role: MessageRole.user, content: userInput },
      ],
      stream: true,
    };

    if (tools.length > 0) {
      openAIOptions.tools = tools;
    }
    const response = await this.openai.responses.create(openAIOptions);

    let finalResponse = "";

    for await (const event of response) {
      console.log("Event type:", event.type);

      if (event.type === "response.output_text.delta") {
        sse.send(event.delta, "content");
        finalResponse += event.delta;
      } else if (event.type === "response.file_search_call.in_progress") {
        sse.send("Searching documents...", "tool_calls_started");
      } else if (event.type === "response.web_search_call.in_progress") {
        sse.send("Searching the web...", "tool_calls_started");
      } else if (event.type === "response.content_part.done") {
        console.log("=== Content Part Done ===");
        console.log("Event:", JSON.stringify(event, null, 2));

        try {
          finalUnifiedMessage =
            await unifiedMessageService.createFromOpenAIResponse(
              event as OpenAIResponseEvent,
              conversationId,
              userId
            );

          if (finalUnifiedMessage) {
            console.log("=== Created UnifiedMessage ===");
            console.log("Message ID:", finalUnifiedMessage.id);
            console.log("Content length:", finalUnifiedMessage.content.length);
            console.log(
              "Annotations count:",
              finalUnifiedMessage.annotations.length
            );
            console.log(
              "References count:",
              finalUnifiedMessage.references.length
            );

            if (finalUnifiedMessage.annotations.length > 0) {
              console.log("=== File Citations ===");
              finalUnifiedMessage.annotations.forEach((ann, idx) => {
                console.log(`Annotation ${idx + 1}:`, {
                  type: ann.type,
                  startIndex: ann.startIndex,
                  endIndex: ann.endIndex,
                  fileId: ann.fileId,
                  filename: ann.filename,
                  documentId: ann.documentId,
                });
              });
            }

            if (finalUnifiedMessage.references.length > 0) {
              console.log("=== Document References ===");
              finalUnifiedMessage.references.forEach((ref, idx) => {
                console.log(`Reference ${idx + 1}:`, {
                  type: ref.type,
                  title: ref.title,
                  filename: ref.filename,
                  documentId: ref.documentId,
                  azureBlobUrl: ref.azureBlobUrl,
                  openaiFileId: ref.openaiFileId,
                });
              });
            }
            sse.send(finalUnifiedMessage, "completed");
          }
        } catch (error) {
          logger.error("Error creating unified message:", error);
          sse.send("Failed to create unified message", "error");
        }
      }
    }
    return finalUnifiedMessage;
  }

  /**
   * Get conversation with messages
   */
  async getConversation(conversationId: string, userId: string) {
    return await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        userId,
      },
      include: {
        unifiedMessages: {
          orderBy: { createdAt: "asc" },
        },
      },
    });
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    userId: string,
    organizationId: string,
    title?: string
  ) {
    return await prisma.conversation.create({
      data: {
        userId,
        organizationId,
        title: title || "New Conversation",
      },
    });
  }

  /**
   * List conversations for a user in an organization
   */
  async listConversations(userId: string, organizationId: string) {
    return await prisma.conversation.findMany({
      where: {
        userId,
        organizationId,
        isArchived: false,
      },
      orderBy: { updatedAt: "desc" },
      take: 50,
    });
  }

  /**
   * Archive a conversation
   */
  async archiveConversation(conversationId: string, userId: string) {
    return await prisma.conversation.update({
      where: {
        id: conversationId,
        userId,
      },
      data: {
        isArchived: true,
      },
    });
  }

  /**
   * Get user's chat settings
   */
  async getChatSettings(
    userId: string,
    organizationId: string
  ): Promise<ChatSettings> {
    try {
      // Temporarily return default settings to avoid Prisma compilation issues
      return {
        enableWebSearch: false,
        personality: undefined,
        systemPrompt: undefined,
      };

      // const settings = await prisma.chatSettings.findUnique({
      //   where: {
      //     userId_organizationId: {
      //       userId,
      //       organizationId,
      //     },
      //   },
      // });

      // return {
      //   enableWebSearch: settings?.enableWebSearch ?? false,
      //   personality: settings?.personality || undefined,
      //   systemPrompt: settings?.systemPrompt || undefined,
      // };
    } catch (error) {
      logger.error("Error getting chat settings:", error);
      // Return default settings on error
      return {
        enableWebSearch: false,
        personality: undefined,
        systemPrompt: undefined,
      };
    }
  }

  /**
   * Update user's chat settings
   */
  async updateChatSettings(
    userId: string,
    organizationId: string,
    settings: Partial<ChatSettings>
  ): Promise<ChatSettings> {
    try {
      logger.info(`Updating chat settings for user ${userId}:`, settings);

      // Temporarily return default settings to avoid Prisma compilation issues
      return {
        enableWebSearch: settings.enableWebSearch ?? false,
        personality: settings.personality,
        systemPrompt: settings.systemPrompt,
      };

      // const updatedSettings = await prisma.chatSettings.upsert({
      //   where: {
      //     userId_organizationId: {
      //       userId,
      //       organizationId,
      //     },
      //   },
      //   update: {
      //     enableWebSearch: settings.enableWebSearch,
      //     personality: settings.personality,
      //     systemPrompt: settings.systemPrompt,
      //   },
      //   create: {
      //     userId,
      //     organizationId,
      //     enableWebSearch: settings.enableWebSearch ?? false,
      //     personality: settings.personality,
      //     systemPrompt: settings.systemPrompt,
      //   },
      // });

      // return {
      //   enableWebSearch: updatedSettings.enableWebSearch,
      //   personality: updatedSettings.personality || undefined,
      //   systemPrompt: updatedSettings.systemPrompt || undefined,
      // };
    } catch (error) {
      logger.error("Error updating chat settings:", error);
      throw new Error("Failed to update chat settings");
    }
  }
}

export const chatService = new ChatService();
export default chatService;
