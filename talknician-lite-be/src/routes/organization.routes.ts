import { Router, Request, Response, NextFunction } from "express";
import { body, param, validationResult } from "express-validator";
import { prisma } from "@/config/database";
import {
  authenticate,
  requireOrganizationMembership,
} from "@/middleware/auth.middleware";
import { logger } from "@/utils/logger";
import { OrganizationRole } from "@prisma/client";

const router: Router = Router();

// Role constants
const ROLE = {
  OWNER: "OWNER",
  MANAGER: "MANAGER",
  MEMBER: "MEMBER",
} as const;

// Validation rules
const updateOrganizationValidation = [
  body("name").optional().isLength({ min: 2, max: 50 }),
  body("description").optional().isLength({ max: 500 }),
  body("website").optional().isURL(),
  body("industry").optional().isLength({ min: 2, max: 50 }),
  body("size").optional().isIn(["1-10", "11-50", "51-200", "201-500", "500+"]),
  body("address").optional().isLength({ max: 200 }),
  body("phone").optional().isLength({ max: 20 }),
  body("email").optional().isEmail(),
  body("timezone").optional().isLength({ min: 2, max: 50 }),
];

const inviteMemberValidation = [
  body("email").isEmail().normalizeEmail(),
  body("role").isIn([ROLE.MEMBER, ROLE.MANAGER, ROLE.OWNER]),
];

/**
 * GET /api/organizations/:id
 * Get organization details
 */
router.get(
  "/:id",
  authenticate,
  requireOrganizationMembership(),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      const organization = await prisma.organization.findUnique({
        where: { id },
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                },
              },
            },
            orderBy: { joinedAt: "asc" },
          },
        },
      });

      if (!organization) {
        return res.status(404).json({
          error: "Organization Not Found",
          message: "Organization does not exist",
        });
      }

      res.json({
        success: true,
        data: {
          organization: {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            description: organization.description,
            avatar: organization.avatar,
            plan: organization.plan,
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
            members: organization.members.map((member) => ({
              id: member.id,
              userId: member.userId,
              role: member.role,
              joinedAt: member.joinedAt,
              user: member.user,
            })),
            memberCount: organization.members.length,
          },
        },
      });
    } catch (error: any) {
      logger.error("Get organization failed", {
        organizationId: req.params.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to fetch organization",
      });
    }
  }
);

/**
 * PUT /api/organizations/:id
 * Update organization details
 */
router.put(
  "/:id",
  authenticate,
  requireOrganizationMembership(ROLE.MANAGER),
  updateOrganizationValidation,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      // Remove undefined fields
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key]
      );

      const organization = await prisma.organization.update({
        where: { id },
        data: updateData,
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                },
              },
            },
          },
        },
      });

      logger.info("Organization updated", {
        organizationId: id,
        userId: req.user!.id,
        updatedFields: Object.keys(updateData),
      });

      res.json({
        success: true,
        message: "Organization updated successfully",
        data: {
          organization: {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            description: organization.description,
            avatar: organization.avatar,
            plan: organization.plan,
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
            memberCount: organization.members.length,
          },
        },
      });
    } catch (error: any) {
      logger.error("Update organization failed", {
        organizationId: req.params.id,
        userId: req.user?.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to update organization",
      });
    }
  }
);

/**
 * POST /api/organizations/:id/invite
 * Invite member to organization
 */
router.post(
  "/:id/invite",
  authenticate,
  requireOrganizationMembership(ROLE.MANAGER),
  inviteMemberValidation,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { id } = req.params;
      const { email, role } = req.body;

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { email },
      });

      // Check if there's already an invitation for this email
      const existingInvitation = await prisma.organizationInvitation.findUnique(
        {
          where: {
            email_organizationId: {
              email,
              organizationId: id,
            },
          },
        }
      );

      if (existingInvitation && !existingInvitation.usedAt) {
        return res.status(409).json({
          error: "Already Invited",
          message: "An invitation has already been sent to this email address",
        });
      }

      if (user) {
        // User exists - check if already a member
        const existingMember = await prisma.organizationMember.findUnique({
          where: {
            userId_organizationId: {
              userId: user.id,
              organizationId: id,
            },
          },
        });

        if (existingMember) {
          return res.status(409).json({
            error: "Already Member",
            message: "User is already a member of this organization",
          });
        }

        // Add existing user to organization immediately
        const member = await prisma.organizationMember.create({
          data: {
            userId: user.id,
            organizationId: id,
            role,
            status: "ACTIVE",
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
              },
            },
          },
        });

        logger.info("Existing user added to organization", {
          organizationId: id,
          invitedUserId: user.id,
          invitedByUserId: req.user!.id,
          role,
        });

        res.status(201).json({
          success: true,
          message: "Member added successfully",
          data: {
            member: {
              id: member.id,
              userId: member.userId,
              role: member.role,
              status: member.status,
              joinedAt: member.joinedAt,
              user: member.user,
            },
          },
        });
      } else {
        // User doesn't exist - create invitation
        const invitation = await prisma.organizationInvitation.create({
          data: {
            email,
            role,
            organizationId: id,
            invitedBy: req.user!.id,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          },
        });

        // Get organization details for the email
        const organization = await prisma.organization.findUnique({
          where: { id },
          select: { name: true },
        });

        // Send invitation email
        try {
          const { emailService } = await import("../services/email.service");
          const inviteLink = `${process.env.FRONTEND_URL}/signup?invitation=${invitation.id}`;
          await emailService.sendInvitationEmail(
            email,
            organization?.name || "Organization",
            inviteLink
          );
        } catch (emailError) {
          logger.warn("Failed to send invitation email", {
            error: emailError,
            invitationId: invitation.id,
          });
          // Don't fail the invitation if email fails
        }

        logger.info("Invitation created for non-existing user", {
          organizationId: id,
          invitedEmail: email,
          invitedByUserId: req.user!.id,
          role,
          invitationId: invitation.id,
        });

        res.status(201).json({
          success: true,
          message: `Invitation sent to ${email}. They will automatically join when they register.`,
          data: {
            invitation: {
              id: invitation.id,
              email: invitation.email,
              role: invitation.role,
              expiresAt: invitation.expiresAt,
              requiresRegistration: true,
            },
          },
        });
      }
    } catch (error: any) {
      logger.error("Invite member failed", {
        organizationId: req.params.id,
        userId: req.user?.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to invite member",
      });
    }
  }
);

/**
 * PUT /api/organizations/:id/members/:memberId
 * Update member role
 */
router.put(
  "/:id/members/:memberId",
  authenticate,
  requireOrganizationMembership(ROLE.MANAGER),
  body("role").isIn([ROLE.MEMBER, ROLE.MANAGER, ROLE.OWNER]),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { id, memberId } = req.params;
      const { role } = req.body;

      // Get current user's role in this organization
      const currentUserMembership = await prisma.organizationMember.findUnique({
        where: {
          userId_organizationId: {
            userId: req.user!.id,
            organizationId: id,
          },
        },
      });

      if (!currentUserMembership) {
        return res.status(403).json({
          error: "Forbidden",
          message: "You are not a member of this organization",
        });
      }

      // Get the member being updated
      const targetMember = await prisma.organizationMember.findUnique({
        where: { id: memberId },
      });

      if (!targetMember) {
        return res.status(404).json({
          error: "Member Not Found",
          message: "Member does not exist",
        });
      }

      // Role hierarchy validation:
      // OWNER > MANAGER > MEMBER
      // Only owners can assign owner role or demote other owners
      if (role === ROLE.OWNER && currentUserMembership.role !== ROLE.OWNER) {
        return res.status(403).json({
          error: "Forbidden",
          message: "Only owners can assign owner role",
        });
      }

      // Only owners can demote other owners
      if (
        targetMember.role === ROLE.OWNER &&
        currentUserMembership.role !== ROLE.OWNER
      ) {
        return res.status(403).json({
          error: "Forbidden",
          message: "Only owners can change other owners' roles",
        });
      }

      // Managers can only manage members (not other managers or owners)
      if (
        currentUserMembership.role === OrganizationRole.MANAGER &&
        targetMember.role !== ROLE.MEMBER
      ) {
        return res.status(403).json({
          error: "Forbidden",
          message: "Managers can only manage member roles",
        });
      }

      // Prevent organization from having no owners
      if (targetMember.role === ROLE.OWNER && role !== ROLE.OWNER) {
        const ownerCount = await prisma.organizationMember.count({
          where: {
            organizationId: id,
            role: ROLE.OWNER,
          },
        });

        if (ownerCount <= 1) {
          return res.status(400).json({
            error: "Cannot Remove Last Owner",
            message: "Organization must have at least one owner",
          });
        }
      }

      const member = await prisma.organizationMember.update({
        where: { id: memberId },
        data: { role },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
      });

      logger.info("Member role updated", {
        organizationId: id,
        memberId,
        newRole: role,
        updatedByUserId: req.user!.id,
      });

      res.json({
        success: true,
        message: "Member role updated successfully",
        data: {
          member: {
            id: member.id,
            userId: member.userId,
            role: member.role,
            joinedAt: member.joinedAt,
            user: member.user,
          },
        },
      });
    } catch (error: any) {
      logger.error("Update member role failed", {
        organizationId: req.params.id,
        memberId: req.params.memberId,
        userId: req.user?.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to update member role",
      });
    }
  }
);

/**
 * DELETE /api/organizations/:id/members/:memberId
 * Remove member from organization
 */
router.delete(
  "/:id/members/:memberId",
  authenticate,
  requireOrganizationMembership(ROLE.MANAGER),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id, memberId } = req.params;

      // Get member details before deletion
      const member = await prisma.organizationMember.findUnique({
        where: { id: memberId },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      });

      if (!member) {
        return res.status(404).json({
          error: "Member Not Found",
          message: "Member does not exist",
        });
      }

      // Get current user's role in this organization for permission checks
      const currentUserMembership = await prisma.organizationMember.findUnique({
        where: {
          userId_organizationId: {
            userId: req.user!.id,
            organizationId: id,
          },
        },
      });

      if (!currentUserMembership) {
        return res.status(403).json({
          error: "Forbidden",
          message: "You are not a member of this organization",
        });
      }

      // Role hierarchy validation for removal:
      // Only owners can remove other owners
      if (
        member.role === ROLE.OWNER &&
        currentUserMembership.role !== ROLE.OWNER
      ) {
        return res.status(403).json({
          error: "Forbidden",
          message: "Only owners can remove other owners",
        });
      }

      // Admins can only remove members (not other admins or owners)
      if (
        currentUserMembership.role === ROLE.MANAGER &&
        member.role !== ROLE.MEMBER
      ) {
        return res.status(403).json({
          error: "Forbidden",
          message: "Admins can only remove members",
        });
      }

      // Prevent removing the last owner
      if (member.role === ROLE.OWNER) {
        const ownerCount = await prisma.organizationMember.count({
          where: {
            organizationId: id,
            role: ROLE.OWNER,
          },
        });

        if (ownerCount <= 1) {
          return res.status(400).json({
            error: "Cannot Remove Last Owner",
            message: "Organization must have at least one owner",
          });
        }
      }

      await prisma.organizationMember.delete({
        where: { id: memberId },
      });

      logger.info("Member removed from organization", {
        organizationId: id,
        removedUserId: member.userId,
        removedByUserId: req.user!.id,
      });

      res.json({
        success: true,
        message: "Member removed successfully",
      });
    } catch (error: any) {
      logger.error("Remove member failed", {
        organizationId: req.params.id,
        memberId: req.params.memberId,
        userId: req.user?.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to remove member",
      });
    }
  }
);

/**
 * DELETE /api/organizations/:id
 * Delete organization (owners only)
 */
router.delete(
  "/:id",
  authenticate,
  requireOrganizationMembership("OWNER"),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      // Delete organization (cascade will handle members, conversations, etc.)
      await prisma.organization.delete({
        where: { id },
      });

      logger.info("Organization deleted", {
        organizationId: id,
        deletedByUserId: req.user!.id,
      });

      res.json({
        success: true,
        message: "Organization deleted successfully",
      });
    } catch (error: any) {
      logger.error("Delete organization failed", {
        organizationId: req.params.id,
        userId: req.user?.id,
        error: error.message,
      });

      res.status(500).json({
        error: "Internal Server Error",
        message: "Failed to delete organization",
      });
    }
  }
);

export default router;
