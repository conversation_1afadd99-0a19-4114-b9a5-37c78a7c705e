/**
 * DEMONSTRATION: How express-sse would simplify the chat streaming endpoint
 * This shows the before/after comparison
 */

import express, { Request, Response } from "express";
import { authenticate } from "../middleware/auth.middleware";
import { chatService } from "../services/chat.service";
import { logger } from "../utils/logger";
import { prisma } from "@/config/database";
import { sseService } from "../services/sse.service";

const router: express.Router = express.Router();
router.use(authenticate);

/**
 * BEFORE: Current manual SSE implementation (lines 215-280 in chat.routes.ts)
 * Problems:
 * - Manual header setup
 * - Manual event formatting
 * - Complex error handling
 * - No connection management
 */

/**
 * AFTER: Simplified with express-sse
 * POST /api/chat-demo/conversations/:id/messages/stream
 * Send message with streaming response using express-sse
 */
router.post(
  "/conversations/:id/messages/stream",
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { message, settings } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!message) {
        return res.status(400).json({
          success: false,
          message: "Message is required",
        });
      }

      // Get organization ID from user context
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          organizationMemberships: {
            include: {
              organization: true,
            },
          },
        },
      });

      if (!user || user.organizationMemberships.length === 0) {
        return res.status(400).json({
          success: false,
          message: "User not associated with any organization",
        });
      }

      const organizationId = user.organizationMemberships[0].organization.id;

      // 🎉 EXPRESS-SSE MAGIC: Create SSE connection with automatic header setup
      const sse = sseService.createConnection(id, userId);
      sse.init(req, res);

      try {
        // Stream the chat response
        const messageStream = chatService.sendMessage(
          id,
          message,
          userId,
          organizationId,
          settings || {}
        );

        for await (const event of messageStream) {
          // 🎉 EXPRESS-SSE MAGIC: Simple send method instead of manual formatting
          switch (event.type) {
            case "content":
              sseService.sendContent(id, userId, event.data.content, event.data.messageId);
              break;
            case "status":
              sseService.sendStatus(id, userId, event.data.status, event.data);
              break;
            case "completed":
              sseService.sendCompletion(id, userId, event.data.messageId, event.data.sources);
              break;
            case "error":
              sseService.sendError(id, userId, event.data.error, event.data);
              break;
          }

          // If it's a completion or error, break the loop
          if (event.type === "completed" || event.type === "error") {
            break;
          }
        }

        // 🎉 EXPRESS-SSE MAGIC: Simple end method
        sseService.sendEnd(id, userId);
        
      } catch (streamError) {
        logger.error("Error in message stream:", streamError);
        // 🎉 EXPRESS-SSE MAGIC: Simple error sending
        sseService.sendError(id, userId, "Streaming failed", { error: streamError });
      }

    } catch (error) {
      logger.error("Error in streaming endpoint:", error);

      // If headers not sent yet, send error response
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Failed to process message",
        });
      } else {
        // 🎉 EXPRESS-SSE MAGIC: Simple error through SSE
        sseService.sendError(req.params.id, req.user?.id || '', "Internal server error", { error });
      }
    }
  }
);

/**
 * COMPARISON SUMMARY:
 * 
 * BEFORE (Manual SSE):
 * - 65+ lines of SSE-specific code
 * - Manual header setup: res.writeHead(200, {...})
 * - Manual event formatting: res.write(`data: ${JSON.stringify(event)}\n\n`)
 * - Complex error handling with headersSent checks
 * - No connection tracking or cleanup
 * 
 * AFTER (Express-SSE):
 * - ~30 lines of business logic
 * - Automatic header setup: sse.init(req, res)
 * - Simple event sending: sseService.sendContent(), sendStatus(), etc.
 * - Built-in error handling and connection management
 * - Automatic connection tracking and cleanup
 * 
 * BENEFITS:
 * ✅ 50% less code
 * ✅ Automatic SSE header management
 * ✅ Built-in event formatting
 * ✅ Connection tracking and cleanup
 * ✅ Better error handling
 * ✅ Type safety with TypeScript
 * ✅ Multiple client support
 * ✅ Initial data support
 * ✅ Retry configuration
 */

export default router;
