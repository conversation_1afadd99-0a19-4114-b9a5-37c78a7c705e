import express, { Request, Response } from "express";
import { body, validationResult } from "express-validator";
import { authenticate } from "../middleware/auth.middleware";
import { webScrapingService } from "../services/webscraping.service";
import { logger } from "../utils/logger";

const router: express.Router = express.Router();

/**
 * @route POST /api/webscraping/add-website
 * @desc Add a website to the RAG system by scraping its content
 * @access Private
 */
router.post(
  "/add-website",
  authenticate,
  [
    body("url")
      .isURL({ protocols: ["http", "https"] })
      .withMessage("Please provide a valid HTTP/HTTPS URL"),
    body("organizationId")
      .isUUID()
      .withMessage("Please provide a valid organization ID"),
  ],
  async (req: Request, res: Response) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { url, organizationId } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      // Validate URL format
      if (!webScrapingService.isValidUrl(url)) {
        return res.status(400).json({
          success: false,
          message: "Invalid URL format",
        });
      }

      logger.info(`User ${userId} requesting to add website: ${url}`);

      // Add website to RAG
      const result = await webScrapingService.addWebsiteToRAG(
        url,
        organizationId,
        userId
      );

      if (result.success) {
        res.status(201).json({
          success: true,
          message: result.message,
          documentId: result.documentId,
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
        });
      }
    } catch (error) {
      logger.error("Error in add-website endpoint:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }
);

/**
 * @route POST /api/webscraping/preview
 * @desc Preview website content before adding to RAG
 * @access Private
 */
router.post(
  "/preview",
  authenticate,
  [
    body("url")
      .isURL({ protocols: ["http", "https"] })
      .withMessage("Please provide a valid HTTP/HTTPS URL"),
  ],
  async (req: Request, res: Response) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { url } = req.body;

      // Validate URL format
      if (!webScrapingService.isValidUrl(url)) {
        return res.status(400).json({
          success: false,
          message: "Invalid URL format",
        });
      }

      logger.info(`Previewing website content: ${url}`);

      // Scrape website for preview
      const scrapedContent = await webScrapingService.scrapeWebsite(url);

      // Return preview with truncated content
      const preview = {
        url: scrapedContent.url,
        title: scrapedContent.title,
        description: scrapedContent.metadata.description,
        contentPreview:
          scrapedContent.content.substring(0, 500) +
          (scrapedContent.content.length > 500 ? "..." : ""),
        contentLength: scrapedContent.content.length,
        metadata: scrapedContent.metadata,
      };

      res.json({
        success: true,
        preview,
      });
    } catch (error) {
      logger.error("Error in preview endpoint:", error);
      res.status(500).json({
        success: false,
        message: "Failed to preview website content",
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
);

/**
 * @route GET /api/webscraping/validate-url
 * @desc Validate if a URL is accessible and scrapable
 * @access Private
 */
router.get(
  "/validate-url",
  authenticate,
  async (req: Request, res: Response) => {
    try {
      const { url } = req.query;

      if (!url || typeof url !== "string") {
        return res.status(400).json({
          success: false,
          message: "URL parameter is required",
        });
      }

      // Basic URL validation
      try {
        new URL(url);
      } catch {
        return res.status(400).json({
          success: false,
          message: "Invalid URL format",
        });
      }

      // Use Crawl4AI service for validation
      const validation = await webScrapingService.validateUrl(url);

      res.json({
        success: true,
        validation: {
          accessible: validation.accessible,
          canScrape: validation.canScrape,
          title: validation.title,
          error: validation.error,
        },
      });
    } catch (error) {
      logger.error("Error in validate-url endpoint:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }
);

export default router;
