import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { auth0Service } from "@/services/auth0.service";
import { prisma } from "@/config/database";
import { logger } from "@/utils/logger";

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        auth0Id: string;
        email: string;
        name?: string;
        avatar?: string;
        organizations: Array<{
          id: string;
          name: string;
          slug: string;
          role: string;
        }>;
      };
    }
  }
}

/**
 * Authentication middleware that verifies JWT tokens and loads user context
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        error: "Unauthorized",
        message: "No valid authorization header found",
      });
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // Verify token with Auth0
    const decoded = await auth0Service.verifyToken(token);

    if (!decoded || !decoded.sub) {
      return res.status(401).json({
        error: "Unauthorized",
        message: "Invalid token",
      });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { auth0Id: decoded.sub },
      include: {
        organizationMemberships: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      // User exists in Auth0 but not in our database
      // This could happen if user was created but not synced
      return res.status(401).json({
        error: "Unauthorized",
        message: "User not found in system",
      });
    }

    // Attach user context to request
    req.user = {
      id: user.id,
      auth0Id: user.auth0Id,
      email: user.email,
      name: user.name || undefined,
      avatar: user.avatar || undefined,
      organizations: user.organizationMemberships.map((membership) => ({
        id: membership.organization.id,
        name: membership.organization.name,
        slug: membership.organization.slug,
        role: membership.role,
      })),
    };

    next();
  } catch (error: any) {
    logger.error("Authentication middleware error", { error: error.message });

    return res.status(401).json({
      error: "Unauthorized",
      message: "Token verification failed",
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return next(); // Continue without user context
  }

  try {
    await authenticate(req, res, next);
  } catch (error) {
    // If authentication fails, continue without user context
    next();
  }
};

/**
 * Organization membership middleware
 * Requires user to be member of specific organization
 */
export const requireOrganizationMembership = (requiredRole?: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: "Unauthorized",
        message: "Authentication required",
      });
    }

    const orgId =
      req.params.organizationId ||
      req.params.id || // Support both :id and :organizationId route params
      req.body.organizationId ||
      req.query.organizationId;

    if (!orgId) {
      return res.status(400).json({
        error: "Bad Request",
        message: "Organization ID required",
      });
    }

    const membership = req.user.organizations.find((org) => org.id === orgId);

    if (!membership) {
      return res.status(403).json({
        error: "Forbidden",
        message: "Not a member of this organization",
      });
    }

    // Check role requirements
    if (requiredRole) {
      const roleHierarchy: { [key: string]: number } = {
        MEMBER: 1,
        MANAGER: 2,
        OWNER: 3,
      };

      const userRole = roleHierarchy[membership.role] || 0;
      const requiredRoleLevel = roleHierarchy[requiredRole] || 999;

      if (userRole < requiredRoleLevel) {
        return res.status(403).json({
          error: "Forbidden",
          message: `${requiredRole} role required`,
        });
      }
    }

    next();
  };
};

/**
 * Rate limiting middleware specifically for authenticated routes
 */
export const authenticatedRateLimit = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Implement custom rate limiting based on user ID
  // This is a placeholder - you might want to use Redis for production
  next();
};
