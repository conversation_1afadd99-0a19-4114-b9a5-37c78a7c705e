import fs from "fs";
import OpenAI from "openai";
import { unifiedMessageService } from "./services/unified-message.service";
import { OpenAIResponseEvent } from "./types/unified-message.types";

const OPENAI_API_KEY =
  "********************************************************************************************************************************************************************";
const OPENAI_MODEL = "ft:gpt-4o-mini-2024-07-18:talknician::AJVThPPd";
const openai = new OpenAI({ apiKey: OPENAI_API_KEY });

const mockVectorStoreId = "vs_685eee73c6648191b1ee34dd0584dab5";
const mockFileId = "file-4J1V6TpZHpDLsBcDZ2DpXy";

const testStreamingText = async () => {
  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: "Hello",
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  for await (const event of response) {
    if (event.type === "response.output_text.delta") {
      console.log(event.delta);
      finalResponse += event.delta;
    } else if (event.type === "response.completed") {
      console.log("Completed");
    } else if (event.type === "error") {
      console.log("Error");
    } else if (event.type === "response.created") {
      console.log("Created");
    }
    console.log("----");
  }
  console.log("Final response:", finalResponse);
};

const uploadFileToServer = async () => {
  const file = await openai.files.create({
    file: fs.createReadStream("quantum.pdf"),
    purpose: "user_data",
  });

  console.log("File uploaded:", file);
  return file;
};

const testStreamingTextWithFileInput = async () => {
  const file = await uploadFileToServer();

  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: [
          {
            type: "input_file",
            file_id: file.id,
          },
          {
            type: "input_text",
            text: "what is the quantum.pdf is about",
          },
        ],
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  for await (const event of response) {
    if (event.type === "response.output_text.delta") {
      console.log(event.delta);
      finalResponse += event.delta;
    } else if (event.type === "response.completed") {
      console.log("Completed");
    } else if (event.type === "error") {
      console.log("Error");
    } else if (event.type === "response.created") {
      console.log("Created");
    }
    console.log("----");
  }
  console.log("Final response:", finalResponse);
};

const testStreamingTextWithWebsearch = async () => {
  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    tools: [{ type: "web_search_preview" }],
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: "Hi",
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  for await (const event of response) {
    if (event.type === "response.output_text.delta") {
      console.log(event.delta);
      finalResponse += event.delta;
    } else if (event.type === "response.completed") {
      console.log("Completed");
    } else if (event.type === "error") {
      console.log("Error");
    } else if (event.type === "response.created") {
      console.log("Created");
    } else if (event.type === "response.web_search_call.in_progress") {
      console.log("Tool calls search");
      console.log(event);
    } else if (event.type === "response.content_part.done") {
      console.log("Output item done");
      console.log(event);
    }
    console.log("----");
  }
  console.log("Final response:", finalResponse);
};

async function createFile(filePath: string) {
  let result;
  if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
    // Download the file content from the URL
    const res = await fetch(filePath);
    const buffer = await res.arrayBuffer();
    const urlParts = filePath.split("/");
    const fileName = urlParts[urlParts.length - 1];
    const file = new File([new Uint8Array(buffer)], fileName);
    result = await openai.files.create({
      file: file,
      purpose: "assistants",
    });
  } else {
    // Handle local file path
    const fileContent = fs.createReadStream(filePath);
    result = await openai.files.create({
      file: fileContent,
      purpose: "assistants",
    });
  }
  return result.id;
}

const createVectorStore = async () => {
  const vectorStore = await openai.vectorStores.create({
    name: "knowledge_base",
  });
  return vectorStore.id;
};

const addFileToVectorStore = async (vectorStoreId: string, fileId: string) => {
  return await openai.vectorStores.files.create(vectorStoreId, {
    file_id: fileId,
  });
};

const uploadFileToVectorDB = async () => {
  const vectorStoreId = await createVectorStore();
  console.log("Vector store created:", vectorStoreId);
  const fileId = await createFile(
    "https://cdn.openai.com/API/docs/deep_research_blog.pdf"
  );
  console.log("File created:", fileId);
  const fileAdded = await addFileToVectorStore(vectorStoreId, fileId);
  console.log(`add ${fileAdded.id} to ${fileAdded.vector_store_id} `);
};

const chatWithRag = async () => {
  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    tools: [{ type: "file_search", vector_store_ids: [mockVectorStoreId] }],
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: "What is deep_research_blog.pdf about ",
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  for await (const event of response) {
    if (event.type === "response.output_text.delta") {
      console.log(event.delta);
      finalResponse += event.delta;
    } else if (event.type === "response.completed") {
      console.log("Completed");
    } else if (event.type === "error") {
      console.log("Error");
    } else if (event.type === "response.created") {
      console.log("Created");
    } else if (event.type === "response.file_search_call.in_progress") {
      console.log("Tool calls file search");
      console.log(event);
    } else if (event.type === "response.content_part.done") {
      console.log("Output item done");
      console.log(event);
    }
    console.log("----");
  }
  console.log("Final response:", finalResponse);
};

// Replace with your own file path or URL

const removeFileFromVector = async () => {
  const deleted = await openai.vectorStores.files.del(
    mockVectorStoreId,
    mockFileId
  );
};
const removeFile = async () => {
  const deleted = await openai.files.del(mockFileId);
  console.log("File deleted", deleted);
};

// Test UnifiedMessage system with web search
const testUnifiedMessageWithWebSearch = async () => {
  console.log("=== Testing UnifiedMessage with Web Search ===");

  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    tools: [{ type: "web_search_preview" }],
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: "What are the latest developments in AI in 2024?",
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  const mockConversationId = "test-conv-123";
  const mockUserId = "test-user-123";

  for await (const event of response) {
    console.log("Event type:", event.type);

    if (event.type === "response.output_text.delta") {
      finalResponse += event.delta;
    } else if (event.type === "response.content_part.done") {
      console.log("=== Content Part Done ===");
      console.log("Event:", JSON.stringify(event, null, 2));

      // Test creating UnifiedMessage from OpenAI response
      try {
        const unifiedMessage =
          await unifiedMessageService.createFromOpenAIResponse(
            event as OpenAIResponseEvent,
            mockConversationId,
            mockUserId
          );

        if (unifiedMessage) {
          console.log("=== Created UnifiedMessage ===");
          console.log("Message ID:", unifiedMessage.id);
          console.log("Content length:", unifiedMessage.content.length);
          console.log("Annotations count:", unifiedMessage.annotations.length);
          console.log("References count:", unifiedMessage.references.length);

          if (unifiedMessage.annotations.length > 0) {
            console.log("=== Annotations ===");
            unifiedMessage.annotations.forEach((ann, idx) => {
              console.log(`Annotation ${idx + 1}:`, {
                type: ann.type,
                startIndex: ann.startIndex,
                endIndex: ann.endIndex,
                url: ann.url,
                title: ann.title,
                documentId: ann.documentId,
                websiteId: ann.websiteId,
              });
            });
          }

          if (unifiedMessage.references.length > 0) {
            console.log("=== References ===");
            unifiedMessage.references.forEach((ref, idx) => {
              console.log(`Reference ${idx + 1}:`, {
                type: ref.type,
                title: ref.title,
                url: ref.url,
                documentId: ref.documentId,
                websiteId: ref.websiteId,
              });
            });
          }
        }
      } catch (error) {
        console.error("Error creating UnifiedMessage:", error);
      }
    }
    console.log("----");
  }

  console.log("Final response length:", finalResponse.length);
};

// Test UnifiedMessage system with file search
const testUnifiedMessageWithFileSearch = async () => {
  console.log("=== Testing UnifiedMessage with File Search ===");

  const response = await openai.responses.create({
    model: "gpt-4.1",
    instructions:
      "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
    tools: [{ type: "file_search", vector_store_ids: [mockVectorStoreId] }],
    input: [
      {
        role: "developer",
        content: "Give user friendly tone",
      },
      {
        role: "user",
        content: "What is deep_research_blog.pdf about?",
      },
    ],
    stream: true,
  });

  let finalResponse = "";
  const mockConversationId = "test-conv-456";
  const mockUserId = "test-user-456";

  for await (const event of response) {
    console.log("Event type:", event.type);

    if (event.type === "response.output_text.delta") {
      finalResponse += event.delta;
    } else if (event.type === "response.content_part.done") {
      console.log("=== Content Part Done ===");
      console.log("Event:", JSON.stringify(event, null, 2));

      // Test creating UnifiedMessage from OpenAI response
      try {
        const unifiedMessage =
          await unifiedMessageService.createFromOpenAIResponse(
            event as OpenAIResponseEvent,
            mockConversationId,
            mockUserId
          );

        if (unifiedMessage) {
          console.log("=== Created UnifiedMessage ===");
          console.log("Message ID:", unifiedMessage.id);
          console.log("Content length:", unifiedMessage.content.length);
          console.log("Annotations count:", unifiedMessage.annotations.length);
          console.log("References count:", unifiedMessage.references.length);

          if (unifiedMessage.annotations.length > 0) {
            console.log("=== File Citations ===");
            unifiedMessage.annotations.forEach((ann, idx) => {
              console.log(`Annotation ${idx + 1}:`, {
                type: ann.type,
                startIndex: ann.startIndex,
                endIndex: ann.endIndex,
                fileId: ann.fileId,
                filename: ann.filename,
                documentId: ann.documentId,
              });
            });
          }

          if (unifiedMessage.references.length > 0) {
            console.log("=== Document References ===");
            unifiedMessage.references.forEach((ref, idx) => {
              console.log(`Reference ${idx + 1}:`, {
                type: ref.type,
                title: ref.title,
                filename: ref.filename,
                documentId: ref.documentId,
                azureBlobUrl: ref.azureBlobUrl,
                openaiFileId: ref.openaiFileId,
              });
            });
          }
        }
      } catch (error) {
        console.error("Error creating UnifiedMessage:", error);
      }
    }
    console.log("----");
  }

  console.log("Final response length:", finalResponse.length);
};
//execute

// removeFile();
// removeFileFromVector();
// testStreamingTextWithFileInput();

// testStreamingText();
// uploadFileToVectorDB();
// testStreamingTextWithWebsearch();
// chatWithRag();

// Test the new UnifiedMessage system
// testUnifiedMessageWithWebSearch();
// testUnifiedMessageWithFileSearch();
