# Crawl4AI Service

A Python microservice that provides advanced web scraping capabilities optimized for AI and RAG applications using [Crawl4AI](https://github.com/unclecode/crawl4ai).

## Features

- **AI-Optimized Content Extraction**: Extracts clean, meaningful content perfect for RAG systems
- **JavaScript Rendering**: Handles dynamic content and SPAs
- **Smart Content Filtering**: Removes boilerplate and focuses on main content
- **Markdown Output**: Provides structured markdown output ideal for LLMs
- **URL Validation**: Checks if URLs are accessible and scrapable
- **Fast API**: RESTful API with automatic documentation

## Installation

### Option 1: Local Development

1. **Install Python 3.11+**
   ```bash
   python3 --version  # Should be 3.11 or higher
   ```

2. **Install dependencies**
   ```bash
   cd crawl4ai-service
   pip install -r requirements.txt
   ```

3. **Start the service**
   ```bash
   python main.py
   # Or use the start script
   ./start.sh
   ```

### Option 2: Docker

1. **Build and run with Docker**
   ```bash
   docker build -t crawl4ai-service .
   docker run -p 8001:8001 crawl4ai-service
   ```

### Option 3: Docker Compose (Recommended)

From the parent directory:
```bash
docker-compose up crawl4ai
```

## API Endpoints

### Health Check
```
GET /health
```

### Scrape Website
```
POST /scrape
Content-Type: application/json

{
  "url": "https://example.com",
  "extract_main_content": true,
  "include_links": false,
  "word_count_threshold": 10
}
```

### Validate URL
```
POST /validate
Content-Type: application/json

{
  "url": "https://example.com"
}
```

## Configuration

Environment variables:
- `PYTHONUNBUFFERED=1` - For proper logging in containers

## Integration with Node.js Backend

The Node.js backend automatically connects to this service via:
- Default URL: `http://localhost:8001`
- Environment variable: `CRAWL4AI_SERVICE_URL`

## Development

1. **API Documentation**: Visit `http://localhost:8001/docs` for interactive API docs
2. **Logs**: The service provides detailed logging for debugging
3. **Health Check**: Use `/health` endpoint to verify service status

## Troubleshooting

1. **Chrome/Chromium Issues**: The service requires Chrome for JavaScript rendering
2. **Memory Usage**: Crawl4AI can be memory-intensive for large pages
3. **Rate Limiting**: Consider implementing rate limiting for production use

## Production Considerations

- Add proper error handling and retry logic
- Implement rate limiting
- Configure resource limits
- Set up monitoring and health checks
- Use a reverse proxy for load balancing
