#!/usr/bin/env python3
"""
Crawl4AI Microservice for Talknician
Provides web scraping capabilities optimized for AI/RAG applications
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from crawl4ai import Async<PERSON>eb<PERSON>rawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Crawl4AI Service",
    description="Web scraping service for AI applications",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request/Response models
class ScrapeRequest(BaseModel):
    url: str
    extract_main_content: bool = True
    include_links: bool = False
    css_selector: Optional[str] = None
    word_count_threshold: int = 10


class ScrapeResponse(BaseModel):
    success: bool
    url: str
    title: str
    content: str
    markdown: str
    metadata: Dict[str, Any]
    links: Optional[list] = None
    error: Optional[str] = None


class HealthResponse(BaseModel):
    status: str
    service: str
    version: str


# Global crawler instance
crawler = None


@app.on_event("startup")
async def startup_event():
    """Initialize the crawler on startup"""
    global crawler
    try:
        crawler = AsyncWebCrawler(verbose=True)
        await crawler.start()
        logger.info("Crawl4AI service started successfully")
    except Exception as e:
        logger.error(f"Failed to start crawler: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler
    if crawler:
        await crawler.close()
        logger.info("Crawl4AI service stopped")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(status="healthy", service="crawl4ai", version="1.0.0")


@app.post("/scrape", response_model=ScrapeResponse)
async def scrape_website(request: ScrapeRequest):
    """
    Scrape a website and extract content optimized for AI/RAG applications
    """
    global crawler

    if not crawler:
        raise HTTPException(status_code=503, detail="Crawler not initialized")

    try:
        url = str(request.url)
        logger.info(f"Scraping URL: {url}")

        # Configure crawling options
        crawl_options = {
            "word_count_threshold": request.word_count_threshold,
            "bypass_cache": True,
            "include_raw_html": False,
        }

        # Add CSS selector if provided
        if request.css_selector:
            crawl_options["css_selector"] = request.css_selector

        # Perform the crawl
        result = await crawler.arun(url=url, **crawl_options)

        if not result.success:
            raise HTTPException(
                status_code=400, detail=f"Failed to crawl URL: {result.error_message}"
            )

        # Extract metadata
        metadata = {
            "description": getattr(result, "description", ""),
            "keywords": getattr(result, "keywords", []),
            "author": getattr(result, "author", ""),
            "language": getattr(result, "language", ""),
            "published_date": getattr(result, "published_date", ""),
            "word_count": (
                len(result.cleaned_html.split()) if result.cleaned_html else 0
            ),
            "crawl_timestamp": getattr(result, "timestamp", ""),
        }

        # Extract links if requested
        links = None
        if request.include_links and hasattr(result, "links"):
            links = result.links

        # Get the main content
        content = result.cleaned_html or ""
        markdown = result.markdown or ""

        # If extract_main_content is True, try to get the most relevant content
        if request.extract_main_content and result.markdown:
            # Use the markdown as it's already cleaned and structured
            content = result.markdown

        return ScrapeResponse(
            success=True,
            url=url,
            title=result.title or "",
            content=content,
            markdown=markdown,
            metadata=metadata,
            links=links,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error scraping {url}: {str(e)}")
        return ScrapeResponse(
            success=False,
            url=str(request.url),
            title="",
            content="",
            markdown="",
            metadata={},
            error=str(e),
        )


@app.post("/validate")
async def validate_url(request: ScrapeRequest):
    """
    Validate if a URL is accessible and scrapable
    """
    global crawler

    if not crawler:
        raise HTTPException(status_code=503, detail="Crawler not initialized")

    try:
        url = str(request.url)
        logger.info(f"Validating URL: {url}")

        # Quick validation crawl
        result = await crawler.arun(
            url=url, word_count_threshold=1, bypass_cache=True, include_raw_html=False
        )

        return {
            "success": True,
            "url": url,
            "accessible": result.success,
            "title": result.title if result.success else None,
            "error": result.error_message if not result.success else None,
            "can_scrape": result.success and bool(result.cleaned_html),
        }

    except Exception as e:
        logger.error(f"Error validating {url}: {str(e)}")
        return {
            "success": False,
            "url": str(request.url),
            "accessible": False,
            "error": str(e),
            "can_scrape": False,
        }


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True, log_level="info")
