{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:29.108Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:29.108Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:29.108Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:40.801Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:40.801Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T00:25:40.802Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:26:41.940Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:26:41.940Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T00:26:41.940Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:32:43.840Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T00:32:43.840Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T00:32:43.840Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)","timestamp":"2025-06-28T00:37:24.149Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","name":"PrismaClientValidationError","service":"talknician-lite-api","stack":"PrismaClientValidationError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at wn (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:37:26.140Z"}
{"level":"error","message":"Error creating message from OpenAI response: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:37:26.141Z"}
{"level":"error","message":"Error creating unified message: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f3945af28819eadc82aaedbeb7b010e546d505b2aa242\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:37:26.141Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)","timestamp":"2025-06-28T00:38:05.191Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","name":"PrismaClientValidationError","service":"talknician-lite-api","stack":"PrismaClientValidationError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at wn (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:38:06.952Z"}
{"level":"error","message":"Error creating message from OpenAI response: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:38:06.953Z"}
{"level":"error","message":"Error creating unified message: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"ASSISTANT\",\n               ~~~~~~~~~~~\n         content: \"Hello! How can I assist you today?\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n         sequenceNumber: 14,\n         metadata: {\n           openaiEvent: {\n             type: \"response.content_part.done\",\n             sequence_number: 14,\n             item_id: \"msg_685f396ea5d881a396b3cec77b2aa80c032f3167621d249f\",\n             output_index: 0,\n             content_index: 0,\n             part: {\n               type: \"output_text\",\n               annotations: [],\n               logprobs: [],\n               text: \"Hello! How can I assist you today?\"\n             }\n           },\n           logprobs: []\n         },\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at UnifiedMessageService.createFromOpenAIResponse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:195:14)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:118:13)","timestamp":"2025-06-28T00:38:06.953Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)","timestamp":"2025-06-28T00:46:30.394Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"hi\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","name":"PrismaClientValidationError","service":"talknician-lite-api","stack":"PrismaClientValidationError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"hi\",\n         conversationId: \"cmcfi2bya0001kums1ulezxe3\",\n         userId: \"cmcerdaf00000av31uoyt8r7x\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at wn (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)","timestamp":"2025-06-28T00:46:30.821Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)","timestamp":"2025-06-28T00:48:16.428Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })","name":"PrismaClientUnknownRequestError","service":"talknician-lite-api","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:7505)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)","timestamp":"2025-06-28T00:48:17.048Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)","timestamp":"2025-06-28T00:49:33.593Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })","name":"PrismaClientUnknownRequestError","service":"talknician-lite-api","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:7505)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)","timestamp":"2025-06-28T00:49:34.226Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })","name":"PrismaClientUnknownRequestError","service":"talknician-lite-api","stack":"PrismaClientUnknownRequestError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:7505)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:221:29)","timestamp":"2025-06-28T00:50:22.224Z"}
{"level":"error","message":"Error in streaming endpoint: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nError occurred during query execution:\nConnectorError(ConnectorError { user_facing_error: None, kind: QueryError(PostgresError { code: \"22P02\", message: \"invalid input value for enum \\\"MessageRole\\\": \\\"user\\\"\", severity: \"ERROR\", detail: None, column: None, hint: None }), transient: false })\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:221:29)","timestamp":"2025-06-28T00:50:22.225Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:10:38.728Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:10:38.728Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T01:10:38.728Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:15:25.912Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:15:25.913Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T01:15:25.913Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:17:47.696Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-06-28T01:17:47.696Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-06-28T01:17:47.696Z"}
{"clientVersion":"5.22.0","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"Hi\",\n         conversationId: \"cmcfjo9ne0005z8ameiog1f0s\",\n         userId: \"cmcfjnxhf0000z8amu52gk7m3\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","name":"PrismaClientValidationError","service":"talknician-lite-api","stack":"PrismaClientValidationError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"Hi\",\n         conversationId: \"cmcfjo9ne0005z8ameiog1f0s\",\n         userId: \"cmcfjnxhf0000z8amu52gk7m3\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at wn (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:221:29)","timestamp":"2025-06-28T01:23:55.985Z"}
{"level":"error","message":"Error in streaming endpoint: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"Hi\",\n         conversationId: \"cmcfjo9ne0005z8ameiog1f0s\",\n         userId: \"cmcfjnxhf0000z8amu52gk7m3\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create({\n       data: {\n         role: \"USER\",\n               ~~~~~~\n         content: \"Hi\",\n         conversationId: \"cmcfjo9ne0005z8ameiog1f0s\",\n         userId: \"cmcfjnxhf0000z8amu52gk7m3\",\n         openaiMessageId: undefined,\n         openaiItemId: undefined,\n         sequenceNumber: undefined,\n         metadata: undefined,\n         annotations: {\n           create: []\n         },\n         references: {\n           create: []\n         }\n       },\n       include: {\n         annotations: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         references: {\n           include: {\n             document: true,\n             website: true\n           }\n         },\n         user: true,\n         conversation: true\n       }\n     })\n\nInvalid value for argument `role`. Expected MessageRole.\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:93:5)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:221:29)","timestamp":"2025-06-28T01:23:55.986Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-28T01:24:32.495Z"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"Error in streaming endpoint: Cannot set headers after they are sent to the client","service":"talknician-lite-api","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:703:11)\n    at ServerResponse.header (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:794:10)\n    at ServerResponse.send (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:174:12)\n    at ServerResponse.json (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js:278:15)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:229:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-28T01:26:16.105Z"}
{"clientVersion":"5.22.0","code":"P2003","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`","meta":{"field_name":"unified_messages_conversation_id_fkey (index)","modelName":"UnifiedMessage"},"name":"PrismaClientKnownRequestError","service":"talknician-lite-api","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:7315)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:92:32)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:220:7)","timestamp":"2025-06-28T14:41:31.837Z"}
{"level":"error","message":"Error in streaming endpoint: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:92:32)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:220:7)","timestamp":"2025-06-28T14:41:31.838Z"}
{"clientVersion":"5.22.0","code":"P2003","level":"error","message":"Error creating unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`","meta":{"field_name":"unified_messages_conversation_id_fkey (index)","modelName":"UnifiedMessage"},"name":"PrismaClientKnownRequestError","service":"talknician-lite-api","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`\n    at $n.handleRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:7315)\n    at $n.handleAndLogRequestError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/@prisma+client@5.22.0_prisma@5.22.0/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:30)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:92:32)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:220:7)","timestamp":"2025-06-28T14:41:55.718Z"}
{"level":"error","message":"Error in streaming endpoint: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`","service":"talknician-lite-api","stack":"Error: Failed to create unified message: \nInvalid `prisma.unifiedMessage.create()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:37:58\n\n  34   `Creating unified message for conversation: ${data.conversationId}`\n  35 );\n  36 \n→ 37 const unifiedMessage = await prisma.unifiedMessage.create(\nForeign key constraint violated: `unified_messages_conversation_id_fkey (index)`\n    at UnifiedMessageService.createUnifiedMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/unified-message.service.ts:110:13)\n    at ChatService.sendMessage (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/services/chat.service.ts:92:32)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/chat.routes.ts:220:7)","timestamp":"2025-06-28T14:41:55.719Z"}
