import { prisma } from "./src/config/database";

// Simple test to verify Prisma client has unifiedMessage
async function testPrismaClient() {
  try {
    // This should work if the Prisma client is properly generated
    const count = await prisma.unifiedMessage.count();
    console.log("UnifiedMessage count:", count);
    
    // Test that the property exists
    console.log("unifiedMessage property exists:", typeof prisma.unifiedMessage);
    
    await prisma.$disconnect();
  } catch (error) {
    console.error("Error:", error);
    await prisma.$disconnect();
  }
}

testPrismaClient();
