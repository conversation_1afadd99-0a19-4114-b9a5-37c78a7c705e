version: '3.8'

services:
  # Main Node.js backend
  backend:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - CRAWL4AI_SERVICE_URL=http://crawl4ai:8001
    depends_on:
      - crawl4ai
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev

  # Crawl4AI Python service
  crawl4ai:
    build: ./crawl4ai-service
    ports:
      - "8001:8001"
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./crawl4ai-service:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: talknician-network
