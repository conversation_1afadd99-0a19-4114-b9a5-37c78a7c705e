# Social Login "User not found in system" Fix

## 🔍 **Root Cause Identified**

The issue is that **social login users are never being created in your database**. Here's what's happening:

### The Problem Flow:

1. ✅ User completes Google/Microsoft OAuth successfully
2. ✅ Auth0 returns JWT token with `sub: "google-oauth2|107632961209860765890"`
3. ✅ Frontend calls `/api/auth/social-login-sync` endpoint
4. ❌ **User creation fails silently** (no logs, no user in database)
5. ❌ Later API calls fail with "User not found in system"

### Database Evidence:

```bash
# Current users in database (all email/password):
👤 User 1: auth0|6858508402cdd0acb4871aca (<EMAIL>)
👤 User 2: auth0|6858524602cdd0acb4871af0 (<EMAIL>)
👤 User 3: auth0|6858527e111889097c7d9a74 (<EMAIL>)
👤 User 4: auth0|68584f3f02cdd0acb4871aaf (<EMAIL>)
👤 User 5: auth0|6858675f02cdd0acb4871cb3 (<EMAIL>)

# Missing: google-oauth2|107632961209860765890 (your Google account)
```

## 🔧 **Immediate Fix Required**

### Issue 1: Social Login Sync Failing Silently

The `social-login-sync` endpoint is being called but not creating users. Possible causes:

1. **Database constraints** (email uniqueness, missing fields)
2. **Prisma connection issues**
3. **Silent exceptions** in the `findOrCreateUserWithAccountLinking` function
4. **Environment/configuration mismatch**

### Issue 2: Missing Email in Token

I noticed in the logs: `"email":"no-email"` - this suggests the JWT token doesn't contain an email, which would cause database creation to fail.

## 🚀 **Quick Fix Steps**

### Step 1: Verify Token Contents

First, let's see what's actually in your JWT token:

```bash
# In frontend browser console after social login:
const token = localStorage.getItem('auth_token');
console.log('Token:', token);

# Decode the token (use jwt.io or):
const payload = JSON.parse(atob(token.split('.')[1]));
console.log('Token payload:', payload);
```

### Step 2: Manual User Creation

Let's create the user manually to unblock you immediately:

```javascript
// Run this in talknician-lite-be directory:
// create-google-user.js
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function createGoogleUser() {
  try {
    const user = await prisma.user.create({
      data: {
        auth0Id: "google-oauth2|107632961209860765890",
        email: "<EMAIL>", // Replace with your actual email
        name: "Your Name", // Replace with your actual name
        avatar: null,
      },
    });

    console.log("✅ Google user created:", user);
  } catch (error) {
    console.error("❌ Error creating user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createGoogleUser();
```

### Step 3: Fix the Root Cause

The enhanced logging I added will help identify why user creation is failing. Check the logs after the next social login attempt.

## 🔍 **Debugging Commands**

```bash
# Check if user exists
node check-specific-user.js

# Monitor logs during social login
tail -f combined.log | grep -E "(Social login sync|findOrCreate|New user created)"

# Test the endpoint directly
curl -X POST http://localhost:8000/api/auth/social-login-sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACTUAL_TOKEN" \
  -d '{
    "auth0Id": "google-oauth2|107632961209860765890",
    "email": "<EMAIL>",
    "name": "Your Name",
    "provider": "google"
  }'
```

## 🎯 **Expected Fix Results**

After fixing:

1. ✅ Social login creates user in database
2. ✅ Subsequent API calls work (no "User not found")
3. ✅ Organization creation works
4. ✅ Full app functionality restored

## 🚨 **Immediate Action**

1. **Run the manual user creation script** to unblock yourself
2. **Test that API calls now work**
3. **Check the enhanced logs** to see why auto-creation failed
4. **Fix the underlying issue** based on log findings

The manual user creation will get you working immediately, then we can fix the automatic creation for future users.
