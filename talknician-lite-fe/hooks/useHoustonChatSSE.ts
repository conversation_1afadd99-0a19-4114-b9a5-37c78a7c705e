import { useState, useCallback, useRef } from "react";
import { ChatSettings } from "./useHoustonChat";
import { useAuth } from "@/contexts/AuthContext";

// Frontend-compatible annotation type
export interface FrontendMessageAnnotation {
  id: string;
  type: "url_citation" | "file_citation";
  startIndex: number;
  endIndex: number;
  url?: string;
  title?: string;
  fileId?: string;
  filename?: string;
  documentId?: string;
  websiteId?: string;
}

// Frontend-compatible reference type
export interface FrontendMessageReference {
  id: string;
  type: "document" | "website";
  title: string;
  documentId?: string;
  azureBlobUrl?: string;
  openaiFileId?: string;
  filename?: string;
  websiteId?: string;
  url?: string;
  scrapedContent?: string;
  metadata?: any;
  createdAt: string;
}

// Response types for API (frontend-compatible format)
export interface ChatMessage {
  id: string;
  role: string;
  content: string;
  conversationId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
  annotations?: FrontendMessageAnnotation[];
  references?: FrontendMessageReference[];
  openaiMessageId?: string;
  openaiItemId?: string;
  sequenceNumber?: number;
  metadata?: any; // JsonValue from Prisma can be any
}

interface StreamingState {
  isStreaming: boolean;
  currentContent: string;
  error: string | null;
  status: string;
  loadingMessage: string;
}

/**
 * Alternative implementation using fetch with EventSource-like API
 * This works around EventSource's POST limitation
 */
class FetchEventSource {
  private controller: AbortController | null = null;

  constructor(
    public url: string,
    public options: {
      method?: string;
      headers?: Record<string, string>;
      body?: string;
      onmessage?: (event: { data: string; type?: string }) => void;
      onerror?: (error: any) => void;
      onopen?: () => void;
    }
  ) {}

  async start() {
    this.controller = new AbortController();

    try {
      const response = await fetch(this.url, {
        method: this.options.method || "GET",
        headers: {
          Accept: "text/event-stream",
          "Cache-Control": "no-cache",
          ...this.options.headers,
        },
        body: this.options.body,
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      this.options.onopen?.();

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("No response body");
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6);
            if (data.trim()) {
              this.options.onmessage?.({ data });
            }
          }
        }
      }
    } catch (error) {
      if ((error as Error)?.name !== "AbortError") {
        this.options.onerror?.(error);
      }
    }
  }

  close() {
    this.controller?.abort();
  }
}

/**
 * BEFORE: Current fetch/ReadableStream implementation (lines 267-432 in useHoustonChat.ts)
 * Problems:
 * - Complex stream parsing with TextDecoder
 * - Manual chunk processing
 * - Complex error handling
 * - No automatic reconnection
 * - Manual connection state management
 */

/**
 * AFTER: Simplified with EventSource API
 * This hook demonstrates how EventSource would simplify SSE consumption
 */
export const useHoustonChatSSE = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    currentContent: "",
    loadingMessage: "",
    error: null,
    status: "idle",
  });

  const { accessToken } = useAuth();
  const fetchEventSourceRef = useRef<FetchEventSource | null>(null);

  const sendMessage = useCallback(
    async ({
      conversationId,
      message,
      settings,
    }: {
      conversationId: string;
      message: string;
      settings?: ChatSettings;
    }) => {
      try {
        setStreamingState({
          isStreaming: true,
          currentContent: "",
          loadingMessage: "Houston is typing...",
          error: null,
          status: "connecting",
        });

        // Add user message immediately
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          content: message,
          role: "user",
          createdAt: new Date().toISOString(),
        };
        setMessages((prev) => [...prev, userMessage]);

        console.log("Sending message:", accessToken);

        // 🎉 SIMPLIFIED SSE: Using FetchEventSource for POST support
        const fetchEventSource = new FetchEventSource(
          `${process.env.NEXT_PUBLIC_API_URL}/api/chat/conversations/${conversationId}/messages/stream`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`, // Add auth if needed
            },
            body: JSON.stringify({ message, settings }),
            onopen: () => {
              console.log("SSE Connected");
              setStreamingState((prev) => ({ ...prev, status: "connected" }));
            },
            onmessage: (event) => {
              try {
                const data = JSON.parse(event.data);

                switch (data.type) {
                  case "content":
                    setStreamingState((prev) => ({
                      ...prev,
                      currentContent: data.content,
                    }));
                    break;

                  case "tool_calls_started":
                    setStreamingState((prev) => ({
                      ...prev,
                      loadingMessage: data.content,
                      status: data.status,
                    }));
                    break;

                  case "completed":
                    // Add assistant message
                    const assistantMessage: ChatMessage = data.data;

                    setMessages((prev) => [...prev, assistantMessage]);
                    setStreamingState({
                      loadingMessage: "",
                      isStreaming: false,
                      currentContent: "",
                      error: null,
                      status: "completed",
                    });
                    fetchEventSource.close();
                    break;

                  case "error":
                    setStreamingState((prev) => ({
                      ...prev,
                      isStreaming: false,
                      error: data.error,
                    }));
                    fetchEventSource.close();
                    break;

                  case "end":
                    fetchEventSource.close();
                    break;
                }
              } catch (parseError) {
                console.error("Error parsing SSE data:", parseError);
              }
            },
            onerror: (error) => {
              console.error("FetchEventSource error:", error);
              setStreamingState((prev) => ({
                ...prev,
                isStreaming: false,
                error: "Connection error",
              }));
            },
          }
        );

        // Store reference for cleanup
        fetchEventSourceRef.current = fetchEventSource;

        // Start the connection
        await fetchEventSource.start();
      } catch (error) {
        console.error("Error sending message:", error);
        setStreamingState((prev) => ({
          ...prev,
          isStreaming: false,
          error: "Failed to send message",
        }));
      }
    },
    [accessToken] // ✅ Fixed: Include accessToken in dependencies
  );

  const stopStreaming = useCallback(() => {
    if (fetchEventSourceRef.current) {
      fetchEventSourceRef.current.close();
      fetchEventSourceRef.current = null;
      setStreamingState((prev) => ({
        ...prev,
        isStreaming: false,
      }));
    }
  }, []);

  return {
    messages,
    streamingState,
    sendMessage,
    stopStreaming,
  };
};

export default useHoustonChatSSE;
