import { useCallback, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useChatStore } from "@/stores/useChatStore";
import { createChatApiClient } from "@/utils/chatApi";
import { useHoustonChatSSE } from "./useHoustonChatSSE";
import { ChatMessage, ChatSettings } from "@/types/chat";

export const useHoustonChatIntegrated = () => {
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  
  // Zustand store
  const {
    currentConversation,
    messages,
    conversations,
    inputValue,
    conversationSearchQuery,
    isMobileConversationsOpen,
    chatSettings,
    currentRequestWebSearch,
    streamingState,
    setCurrentConversation,
    setMessages,
    addMessage,
    setConversations,
    addConversation,
    removeConversation,
    setInputValue,
    setConversationSearchQuery,
    setIsMobileConversationsOpen,
    setChatSettings,
    setCurrentRequestWebSearch,
    setStreamingState,
    resetMessages,
  } = useChatStore();

  // SSE hook for streaming
  const { sendMessage: sendSSEMessage, stopStreaming } = useHoustonChatSSE();

  // API client
  const apiClient = createChatApiClient(accessToken);

  // Load conversations
  const loadConversations = useCallback(async () => {
    if (!currentOrganization || !accessToken) return;
    
    try {
      const conversations = await apiClient.getConversations(currentOrganization.id);
      setConversations(conversations);
      
      // Auto-select first conversation if none selected
      if (conversations.length > 0 && !currentConversation) {
        await loadConversation(conversations[0].id);
      }
    } catch (error) {
      console.error("Failed to load conversations:", error);
      setStreamingState({ error: "Failed to load conversations" });
    }
  }, [currentOrganization, accessToken, currentConversation]);

  // Load specific conversation
  const loadConversation = useCallback(async (conversationId: string) => {
    if (!accessToken) return;
    
    try {
      setStreamingState({ error: null });
      const { conversation, messages } = await apiClient.getConversation(conversationId);
      setCurrentConversation(conversation);
      setMessages(messages);
    } catch (error) {
      console.error("Failed to load conversation:", error);
      setStreamingState({ error: "Failed to load conversation" });
    }
  }, [accessToken]);

  // Create new conversation
  const createNewConversation = useCallback(async () => {
    if (!accessToken) return;
    
    try {
      const newConversation = await apiClient.createConversation();
      addConversation(newConversation);
      setCurrentConversation(newConversation);
      resetMessages();
    } catch (error) {
      console.error("Failed to create conversation:", error);
      setStreamingState({ error: "Failed to create conversation" });
    }
  }, [accessToken]);

  // Delete conversation
  const deleteConversation = useCallback(async (conversationId: string) => {
    if (!accessToken) return;
    
    try {
      await apiClient.deleteConversation(conversationId);
      removeConversation(conversationId);
      
      // If deleted conversation was current, load another one
      if (currentConversation?.id === conversationId) {
        const remainingConversations = conversations.filter(c => c.id !== conversationId);
        if (remainingConversations.length > 0) {
          await loadConversation(remainingConversations[0].id);
        } else {
          setCurrentConversation(null);
          resetMessages();
        }
      }
    } catch (error) {
      console.error("Failed to delete conversation:", error);
      setStreamingState({ error: "Failed to delete conversation" });
    }
  }, [accessToken, currentConversation, conversations]);

  // Load chat settings
  const loadChatSettings = useCallback(async () => {
    if (!currentOrganization || !accessToken) return;
    
    try {
      const settings = await apiClient.getChatSettings(currentOrganization.id);
      setChatSettings(settings);
    } catch (error) {
      console.error("Failed to load chat settings:", error);
    }
  }, [currentOrganization, accessToken]);

  // Update chat settings
  const updateChatSettings = useCallback(async (newSettings: Partial<ChatSettings>) => {
    if (!currentOrganization || !accessToken) return;
    
    try {
      const updatedSettings = await apiClient.updateChatSettings(currentOrganization.id, newSettings);
      setChatSettings(updatedSettings);
    } catch (error) {
      console.error("Failed to update chat settings:", error);
      setStreamingState({ error: "Failed to update chat settings" });
    }
  }, [currentOrganization, accessToken]);

  // Send message
  const sendMessage = useCallback(async () => {
    if (!inputValue.trim() || !currentConversation || !accessToken || streamingState.isStreaming) {
      return;
    }

    const messageText = inputValue.trim();
    setInputValue("");
    setStreamingState({ isStreaming: true, error: null });

    // Add user message immediately
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: messageText,
      role: "user",
      createdAt: new Date().toISOString(),
      conversationId: currentConversation.id,
    };
    addMessage(userMessage);

    try {
      // Use SSE for streaming response
      await sendSSEMessage({
        conversationId: currentConversation.id,
        message: messageText,
        settings: {
          ...chatSettings,
          enableWebSearch: currentRequestWebSearch,
        },
      });
      
      // Reset web search toggle after sending
      setCurrentRequestWebSearch(false);
    } catch (error) {
      console.error("Failed to send message:", error);
      setStreamingState({ 
        isStreaming: false, 
        error: "Failed to send message" 
      });
    }
  }, [
    inputValue,
    currentConversation,
    accessToken,
    streamingState.isStreaming,
    chatSettings,
    currentRequestWebSearch,
    sendSSEMessage,
  ]);

  // Load data on mount and when organization changes
  useEffect(() => {
    if (currentOrganization && accessToken) {
      loadConversations();
      loadChatSettings();
    }
  }, [currentOrganization, accessToken]);

  // Filter conversations based on search
  const filteredConversations = conversations.filter((conv) =>
    conv.title.toLowerCase().includes(conversationSearchQuery.toLowerCase())
  );

  return {
    // State
    currentConversation,
    messages,
    conversations: filteredConversations,
    inputValue,
    conversationSearchQuery,
    isMobileConversationsOpen,
    chatSettings,
    currentRequestWebSearch,
    streamingState,
    
    // Actions
    setInputValue,
    setConversationSearchQuery,
    setIsMobileConversationsOpen,
    setCurrentRequestWebSearch,
    loadConversation,
    createNewConversation,
    deleteConversation,
    updateChatSettings,
    sendMessage,
    stopStreaming,
  };
};
