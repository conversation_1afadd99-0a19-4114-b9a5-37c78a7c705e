import { useState, useRef, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";

export interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  createdAt: string;
  isStreaming?: boolean;
  sources?: SourceReference[];
}

export interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

export interface ChatSettings {
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
}

export interface SourceReference {
  type: "document" | "web" | "general";
  title: string;
  content?: string;
  url?: string;
  documentId?: string;
}

export function useHoustonChat() {
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] =
    useState<Conversation | null>(null);
  const [loadingMessage, setLoadingMessage] = useState("Houston is typing...");
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(
    null
  );
  const [isMobileConversationsOpen, setIsMobileConversationsOpen] =
    useState(false);
  const [conversationSearchQuery, setConversationSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    enableWebSearch: false,
    personality: undefined,
    systemPrompt: undefined,
  });
  const [sources, setSources] = useState<SourceReference[]>([]);
  const [currentRequestWebSearch, setCurrentRequestWebSearch] = useState(false);

  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";

  const getAuthHeaders = () => ({
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`,
  });

  // Load conversations from backend
  const loadConversations = useCallback(async () => {
    if (!currentOrganization || !accessToken) return;
    try {
      setIsLoading(true);
      const response = await fetch(
        `${API_BASE_URL}/api/chat/conversations?organizationId=${currentOrganization?.id}`,
        { headers: getAuthHeaders() }
      );
      if (response.ok) {
        const data = await response.json();
        setConversations(data.data.conversations || []);
        if (
          data.data.conversations &&
          data.data.conversations.length > 0 &&
          !currentConversation
        ) {
          await loadConversation(data.data.conversations[0].id);
        }
      } else {
        setError("Failed to load conversations");
      }
    } catch (err) {
      setError("Network error while loading conversations");
    } finally {
      setIsLoading(false);
    }
  }, [currentOrganization, accessToken]);

  // Load specific conversation with messages
  const loadConversation = useCallback(
    async (conversationId: string) => {
      if (!accessToken) return;
      try {
        setIsLoading(true);
        const response = await fetch(
          `${API_BASE_URL}/api/chat/conversations/${conversationId}`,
          { headers: getAuthHeaders() }
        );
        if (response.ok) {
          const data = await response.json();
          setCurrentConversation(data.data.conversation);
          setMessages(data.data.messages || []);
          setError(null);
        } else {
          setError("Failed to load conversation");
        }
      } catch (err) {
        setError("Network error while loading conversation");
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken]
  );

  // Create new conversation
  const createNewConversation = useCallback(async () => {
    if (!accessToken) return;
    try {
      const response = await fetch(`${API_BASE_URL}/api/chat/conversations`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({ title: "New Conversation" }),
      });
      if (response.ok) {
        const data = await response.json();
        const newConversation = data.data;
        setConversations((prev) => [newConversation, ...prev]);
        setCurrentConversation(newConversation);
        setMessages([]);
        setError(null);
      } else {
        setError("Failed to create new conversation");
      }
    } catch (err) {
      setError("Network error while creating conversation");
    }
  }, [accessToken]);

  // Delete conversation
  const deleteConversation = useCallback(
    async (conversationId: string) => {
      if (!accessToken) return;
      try {
        const response = await fetch(
          `${API_BASE_URL}/api/chat/conversations/${conversationId}`,
          { method: "DELETE", headers: getAuthHeaders() }
        );
        if (response.ok) {
          setConversations((prev) =>
            prev.filter((conv) => conv.id !== conversationId)
          );
          if (currentConversation?.id === conversationId) {
            const remainingConversations = conversations.filter(
              (conv) => conv.id !== conversationId
            );
            if (remainingConversations.length > 0) {
              await loadConversation(remainingConversations[0].id);
            } else {
              setCurrentConversation(null);
              setMessages([]);
            }
          }
        } else {
          setError("Failed to delete conversation");
        }
      } catch (err) {
        setError("Network error while deleting conversation");
      }
    },
    [accessToken, currentConversation, conversations, loadConversation]
  );

  // Load chat settings
  const loadChatSettings = useCallback(async () => {
    if (!currentOrganization || !accessToken) return;
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/chat/settings?organizationId=${currentOrganization.id}`,
        { headers: getAuthHeaders() }
      );
      if (response.ok) {
        const data = await response.json();
        setChatSettings(data.data.settings || {});
      } else {
        console.error("Failed to load chat settings");
      }
    } catch (err) {
      console.error("Network error while loading chat settings:", err);
    }
  }, [currentOrganization, accessToken]);

  // Update chat settings
  const updateChatSettings = useCallback(
    async (newSettings: Partial<ChatSettings>) => {
      if (!currentOrganization || !accessToken) return;
      try {
        const response = await fetch(`${API_BASE_URL}/api/chat/settings`, {
          method: "PUT",
          headers: getAuthHeaders(),
          body: JSON.stringify({
            organizationId: currentOrganization.id,
            settings: newSettings,
          }),
        });
        if (response.ok) {
          const data = await response.json();
          setChatSettings(data.data.settings || {});
        } else {
          setError("Failed to update chat settings");
        }
      } catch (err) {
        setError("Network error while updating chat settings");
      }
    },
    [currentOrganization, accessToken]
  );

  // Send message with streaming
  const sendMessage = useCallback(async () => {
    if (
      !inputValue.trim() ||
      !currentConversation ||
      !accessToken ||
      isLoading ||
      isStreaming
    )
      return;

    const userMessage: Message = {
      id: `temp-user-${Date.now()}`,
      content: inputValue,
      role: "user",
      createdAt: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const messageText = inputValue;
    setInputValue("");
    setIsStreaming(true);
    setError(null);
    setCurrentRequestWebSearch(false); // Reset after sending

    // Create streaming assistant message
    const assistantMessage: Message = {
      id: `temp-assistant-${Date.now()}`,
      content: "",
      role: "assistant",
      createdAt: new Date().toISOString(),
      isStreaming: true,
    };

    setMessages((prev) => [...prev, assistantMessage]);
    setStreamingMessageId(assistantMessage.id);

    try {
      // Use fetch with streaming instead of EventSource
      const response = await fetch(
        `${API_BASE_URL}/conversations/${currentConversation.id}/messages/stream`,
        {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify({
            message: messageText,
            settings: {
              ...chatSettings,
              enableWebSearch: currentRequestWebSearch,
            },
          }),
        }
      );
      const es = new EventSource(
        `${API_BASE_URL}/conversations/${currentConversation.id}/messages/stream`
      );
      es.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === "content") {
          setMessages((prev) =>
            prev.map((msg) =>
              msg.id === assistantMessage.id
                ? { ...msg, content: data.content }
                : msg
            )
          );
        }
      };
    } catch (err) {
      console.error("Error setting up stream:", err);
      setError("Failed to start streaming");
      setMessages((prev) =>
        prev.filter((msg) => msg.id !== assistantMessage.id)
      );
      setIsStreaming(false);
      setStreamingMessageId(null);
    }
  }, [
    inputValue,
    currentConversation,
    accessToken,
    isLoading,
    isStreaming,
    currentOrganization,
    loadConversations,
    chatSettings,
  ]);

  // Load conversations on mount
  useEffect(() => {
    if (currentOrganization && accessToken) {
      loadConversations();
      loadChatSettings();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentOrganization, accessToken]);

  // Filter conversations based on search
  const filteredConversations = conversations.filter((conv) =>
    conv.title.toLowerCase().includes(conversationSearchQuery.toLowerCase())
  );

  return {
    conversations,
    filteredConversations,
    currentConversation,
    setCurrentConversation,
    messages,
    setMessages,
    inputValue,
    setInputValue,
    isLoading,
    isStreaming,
    setIsStreaming,
    streamingMessageId,
    setStreamingMessageId,
    isMobileConversationsOpen,
    setIsMobileConversationsOpen,
    conversationSearchQuery,
    setConversationSearchQuery,
    error,
    setError,
    chatSettings,
    setChatSettings,
    sources,
    setSources,
    currentRequestWebSearch,
    setCurrentRequestWebSearch,
    loadConversations,
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    loadChatSettings,
    updateChatSettings,
  };
}
