"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useAuth } from "./AuthContext";

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: string;
  plan?: string;
  description?: string;
  avatar?: string;
  createdAt?: string;
}

interface OrganizationContextType {
  currentOrganization: Organization | null;
  organizations: Organization[];
  setCurrentOrganization: (org: Organization) => void;
  refreshOrganizations: () => Promise<void>;
  isLoading: boolean;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined
);

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [currentOrganization, setCurrentOrganization] =
    useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize organizations when user is loaded
  useEffect(() => {
    if (isAuthenticated && user?.organizations) {
      setOrganizations(user.organizations);

      // Set current organization from localStorage or first organization
      const savedOrgId = localStorage.getItem("current_organization_id");
      const savedOrg = user.organizations.find((org) => org.id === savedOrgId);

      if (savedOrg) {
        setCurrentOrganization(savedOrg);
      } else if (user.organizations.length > 0) {
        setCurrentOrganization(user.organizations[0]);
        localStorage.setItem(
          "current_organization_id",
          user.organizations[0].id
        );
      }
    } else {
      setOrganizations([]);
      setCurrentOrganization(null);
    }
  }, [isAuthenticated, user]);

  const handleSetCurrentOrganization = (org: Organization) => {
    setCurrentOrganization(org);
    localStorage.setItem("current_organization_id", org.id);
  };

  const refreshOrganizations = async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const userOrgs = data.data.user.organizations || [];
        setOrganizations(userOrgs);

        // Update current organization if it still exists
        if (currentOrganization) {
          const updatedCurrentOrg = userOrgs.find(
            (org: Organization) => org.id === currentOrganization.id
          );
          if (updatedCurrentOrg) {
            setCurrentOrganization(updatedCurrentOrg);
          } else if (userOrgs.length > 0) {
            handleSetCurrentOrganization(userOrgs[0]);
          }
        }
      }
    } catch (error) {
      console.error("Failed to refresh organizations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const value: OrganizationContextType = {
    currentOrganization,
    organizations,
    setCurrentOrganization: handleSetCurrentOrganization,
    refreshOrganizations,
    isLoading,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider"
    );
  }
  return context;
}
