// Chat-related types for Houston AI Chat

export interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  conversationId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
  annotations?: MessageAnnotation[];
  references?: MessageReference[];
  openaiMessageId?: string;
  openaiItemId?: string;
  sequenceNumber?: number;
  metadata?: any;
}

export interface MessageAnnotation {
  id: string;
  type: "url_citation" | "file_citation";
  startIndex: number;
  endIndex: number;
  url?: string;
  title?: string;
  fileId?: string;
  filename?: string;
  documentId?: string;
  websiteId?: string;
}

export interface MessageReference {
  id: string;
  type: "document" | "website";
  title: string;
  documentId?: string;
  azureBlobUrl?: string;
  openaiFileId?: string;
  filename?: string;
  websiteId?: string;
  url?: string;
  scrapedContent?: string;
  metadata?: any;
  createdAt: string;
}

export interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  userId: string;
  messages?: ChatMessage[];
}

export interface ChatSettings {
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
}

export interface StreamingState {
  isStreaming: boolean;
  currentContent: string;
  error: string | null;
  status: string;
  loadingMessage: string;
}

// API Response types
export interface ConversationsResponse {
  success: boolean;
  data: {
    conversations: Conversation[];
  };
}

export interface ConversationResponse {
  success: boolean;
  data: {
    conversation: Conversation;
    messages: ChatMessage[];
  };
}

export interface CreateConversationResponse {
  success: boolean;
  data: Conversation;
}

export interface ChatSettingsResponse {
  success: boolean;
  data: ChatSettings;
}
