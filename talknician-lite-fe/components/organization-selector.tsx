"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Building, ChevronDown, Crown, Shield, User, Plus } from "lucide-react";
import { useOrganization } from "@/contexts/OrganizationContext";
import { cn } from "@/lib/utils";

interface OrganizationSelectorProps {
  className?: string;
}

export function OrganizationSelector({ className }: OrganizationSelectorProps) {
  const { currentOrganization, organizations, setCurrentOrganization } =
    useOrganization();

  const getRoleIcon = (role: string) => {
    switch (role.toUpperCase()) {
      case "OWNER":
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case "MANAGER":
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return <User className="w-3 h-3 text-slate-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    const roleUpper = role.toUpperCase();
    const colors = {
      OWNER:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      MANAGER: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      MEMBER:
        "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200",
    };

    return (
      <Badge
        variant="secondary"
        className={cn(
          "text-xs font-medium",
          colors[roleUpper as keyof typeof colors] || colors.MEMBER
        )}
      >
        {roleUpper}
      </Badge>
    );
  };

  if (!currentOrganization) {
    return (
      <div
        className={cn(
          "flex items-center justify-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg",
          className
        )}
      >
        <span className="text-sm text-slate-500 dark:text-slate-400">
          No organization selected
        </span>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-between p-3 h-auto bg-slate-50 dark:bg-slate-800 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg",
            className
          )}
        >
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <Avatar className="w-8 h-8 flex-shrink-0">
              <AvatarImage src={currentOrganization.avatar} />
              <AvatarFallback className="bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400">
                <Building className="w-4 h-4" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0 text-left">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-slate-900 dark:text-white truncate">
                  {currentOrganization.name}
                </span>
                {getRoleIcon(currentOrganization.role)}
              </div>
              <div className="flex items-center space-x-2 mt-1">
                {getRoleBadge(currentOrganization.role)}
                {currentOrganization.plan && (
                  <Badge variant="outline" className="text-xs">
                    {currentOrganization.plan}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <ChevronDown className="w-4 h-4 text-slate-400 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-80">
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {organizations.map((org) => (
          <DropdownMenuItem
            key={org.id}
            onClick={() => setCurrentOrganization(org)}
            className={cn(
              "p-3 cursor-pointer",
              currentOrganization.id === org.id &&
                "bg-indigo-50 dark:bg-indigo-900/20"
            )}
          >
            <div className="flex items-center space-x-3 w-full">
              <Avatar className="w-8 h-8 flex-shrink-0">
                <AvatarImage src={org.avatar} />
                <AvatarFallback className="bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400">
                  <Building className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-slate-900 dark:text-white truncate">
                    {org.name}
                  </span>
                  {getRoleIcon(org.role)}
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  {getRoleBadge(org.role)}
                  {org.plan && (
                    <Badge variant="outline" className="text-xs">
                      {org.plan}
                    </Badge>
                  )}
                </div>
              </div>
              {currentOrganization.id === org.id && (
                <div className="w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0" />
              )}
            </div>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuItem className="p-3 cursor-pointer text-indigo-600 dark:text-indigo-400">
          <Plus className="w-4 h-4 mr-3" />
          Create New Organization
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
