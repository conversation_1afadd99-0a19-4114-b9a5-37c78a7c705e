"use client";

import React from "react";
import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Bo<PERSON>, User, ExternalLink, FileText } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { ChatMessage as ChatMessageType } from "@/types/chat";

interface ChatMessageProps {
  message: ChatMessageType;
  isStreaming?: boolean;
  streamingContent?: string;
}

export function ChatMessage({ message, isStreaming, streamingContent }: ChatMessageProps) {
  const isUser = message.role === "user";
  const displayContent = isStreaming ? streamingContent : message.content;

  return (
    <div className={`flex gap-3 ${isUser ? "justify-end" : "justify-start"}`}>
      {!isUser && (
        <Avatar className="w-8 h-8 mt-1">
          <AvatarFallback className="bg-indigo-100 text-indigo-600">
            <Bot className="w-4 h-4" />
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className={`max-w-[80%] ${isUser ? "order-first" : ""}`}>
        <div
          className={`rounded-lg px-4 py-3 ${
            isUser
              ? "bg-indigo-600 text-white ml-auto"
              : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          }`}
        >
          {isUser ? (
            <p className="whitespace-pre-wrap">{displayContent}</p>
          ) : (
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <ReactMarkdown
                components={{
                  p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                  ul: ({ children }) => <ul className="mb-2 last:mb-0 pl-4">{children}</ul>,
                  ol: ({ children }) => <ol className="mb-2 last:mb-0 pl-4">{children}</ol>,
                  li: ({ children }) => <li className="mb-1">{children}</li>,
                  code: ({ children, className }) => {
                    const isInline = !className;
                    return isInline ? (
                      <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm">
                        {children}
                      </code>
                    ) : (
                      <pre className="bg-gray-200 dark:bg-gray-700 p-3 rounded-md overflow-x-auto">
                        <code>{children}</code>
                      </pre>
                    );
                  },
                }}
              >
                {displayContent || ""}
              </ReactMarkdown>
            </div>
          )}
          
          {isStreaming && !isUser && (
            <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }} />
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }} />
              </div>
              <span>Houston is typing...</span>
            </div>
          )}
        </div>

        {/* References */}
        {message.references && message.references.length > 0 && (
          <div className="mt-2 space-y-1">
            <p className="text-xs text-gray-500 font-medium">Sources:</p>
            <div className="flex flex-wrap gap-2">
              {message.references.map((ref) => (
                <Badge
                  key={ref.id}
                  variant="outline"
                  className="text-xs cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    if (ref.url) {
                      window.open(ref.url, "_blank");
                    }
                  }}
                >
                  {ref.type === "document" ? (
                    <FileText className="w-3 h-3 mr-1" />
                  ) : (
                    <ExternalLink className="w-3 h-3 mr-1" />
                  )}
                  {ref.title}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Timestamp */}
        {message.createdAt && (
          <p className="text-xs text-gray-500 mt-1">
            {new Date(message.createdAt).toLocaleTimeString()}
          </p>
        )}
      </div>

      {isUser && (
        <Avatar className="w-8 h-8 mt-1">
          <AvatarFallback className="bg-blue-100 text-blue-600">
            <User className="w-4 h-4" />
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}
