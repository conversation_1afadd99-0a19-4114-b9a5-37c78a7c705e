"use client";

import React, { KeyboardEvent } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Send, Globe, Loader2, Square } from "lucide-react";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onStop?: () => void;
  isStreaming: boolean;
  isLoading?: boolean;
  webSearchEnabled: boolean;
  onWebSearchToggle: (enabled: boolean) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onStop,
  isStreaming,
  isLoading = false,
  webSearchEnabled,
  onWebSearchToggle,
  disabled = false,
  placeholder = "Ask Houston anything...",
}: ChatInputProps) {
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!isStreaming && !isLoading && value.trim()) {
        onSend();
      }
    }
  };

  const handleSend = () => {
    if (!isStreaming && !isLoading && value.trim()) {
      onSend();
    }
  };

  const handleStop = () => {
    if (isStreaming && onStop) {
      onStop();
    }
  };

  return (
    <div className="border-t bg-white dark:bg-gray-900 p-4">
      {/* Web Search Toggle */}
      <div className="flex items-center space-x-2 mb-3">
        <Switch
          id="web-search"
          checked={webSearchEnabled}
          onCheckedChange={onWebSearchToggle}
          disabled={disabled || isStreaming}
        />
        <Label htmlFor="web-search" className="text-sm flex items-center gap-2">
          <Globe className="w-4 h-4" />
          Search the web for this message
        </Label>
      </div>

      {/* Input Area */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="min-h-[60px] max-h-[200px] resize-none"
            rows={2}
          />
        </div>
        
        <div className="flex flex-col gap-2">
          {isStreaming ? (
            <Button
              onClick={handleStop}
              variant="outline"
              size="icon"
              className="h-[60px] w-12"
              disabled={!onStop}
            >
              <Square className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSend}
              disabled={disabled || isLoading || !value.trim()}
              className="h-[60px] w-12"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Status indicator */}
      {isStreaming && (
        <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-indigo-500 rounded-full animate-bounce" />
            <div className="w-1 h-1 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }} />
            <div className="w-1 h-1 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }} />
          </div>
          <span>Houston is responding...</span>
        </div>
      )}
    </div>
  );
}
