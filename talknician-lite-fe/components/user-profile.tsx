"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  LogOut,
  Settings,
  User,
  ChevronDown,
  Crown,
  Shield,
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";

interface UserProfileProps {
  className?: string;
  showName?: boolean;
}

export function UserProfile({
  className = "",
  showName = true,
}: UserProfileProps) {
  const { user, logout } = useAuth();
  const { currentOrganization } = useOrganization();

  if (!user) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getCurrentUserRole = () => {
    if (!currentOrganization || !user.organizations) return null;
    const org = user.organizations.find((o) => o.id === currentOrganization.id);
    return org?.role;
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case "owner":
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case "manager":
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return null;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role.toLowerCase()) {
      case "owner":
        return "Owner";
      case "manager":
        return "Manager";
      default:
        return null;
    }
  };

  const handleLogout = () => {
    logout();
    // Redirect to home page after logout
    window.location.href = "/";
  };

  const userRole = getCurrentUserRole();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`flex items-center space-x-2 h-auto p-2 hover:bg-slate-100 dark:hover:bg-slate-800 ${className}`}
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.picture} alt={user.name} />
            <AvatarFallback className="bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 text-xs">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
          {showName && (
            <div className="flex flex-col items-start min-w-0 flex-1">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300 truncate max-w-32">
                {user.name}
              </span>
              {userRole && (
                <div className="flex items-center space-x-1 mt-0.5">
                  {getRoleIcon(userRole)}
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {getRoleLabel(userRole)}
                  </span>
                </div>
              )}
            </div>
          )}
          {showName && (
            <ChevronDown className="w-4 h-4 text-slate-400 !ml-auto" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/settings/profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            Profile
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleLogout}
          className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
