"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PlusIcon, MessageSquare, Loader2 } from "lucide-react";
import {
  useAppStore,
  useCurrentOrganization,
  useActiveConversation,
  useAppActions,
} from "@/stores/useAppStore";
import {
  useConversationsQuery,
  useCreateConversationMutation,
} from "@/lib/api-client";

export function ChatSidebar() {
  const currentOrganization = useCurrentOrganization();
  const activeConversationId = useActiveConversation();
  const { setActiveConversation, openModal, addNotification } = useAppActions();

  // Server state - fetch conversations
  const {
    data: conversationsResponse,
    isLoading,
    error,
  } = useConversationsQuery(currentOrganization?.id || "");

  const conversations = conversationsResponse?.data || [];

  // Mutation for creating new conversation
  const createConversationMutation = useCreateConversationMutation();

  const handleCreateConversation = async () => {
    if (!currentOrganization) {
      addNotification({
        type: "error",
        message: "Please select an organization first",
      });
      return;
    }

    try {
      const response = await createConversationMutation.mutateAsync({
        title: "New Conversation",
        organizationId: currentOrganization.id,
      });

      // Set as active conversation
      setActiveConversation(response.data.id);

      addNotification({
        type: "success",
        message: "New conversation created!",
      });
    } catch (error) {
      addNotification({
        type: "error",
        message: "Failed to create conversation",
      });
    }
  };

  if (!currentOrganization) {
    return (
      <div className="w-64 border-r bg-slate-50 dark:bg-slate-900 p-4">
        <p className="text-sm text-slate-500 dark:text-slate-400">
          Select an organization to view conversations
        </p>
      </div>
    );
  }

  return (
    <div className="w-64 border-r bg-slate-50 dark:bg-slate-900 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <Button
          onClick={handleCreateConversation}
          disabled={createConversationMutation.isPending}
          className="w-full"
        >
          {createConversationMutation.isPending ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <PlusIcon className="w-4 h-4 mr-2" />
          )}
          New Chat
        </Button>
      </div>

      {/* Conversations List */}
      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="p-4 flex items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin" />
          </div>
        ) : error ? (
          <div className="p-4">
            <p className="text-sm text-red-500">Failed to load conversations</p>
          </div>
        ) : conversations.length === 0 ? (
          <div className="p-4">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              No conversations yet. Create your first one!
            </p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => (
              <Button
                key={conversation.id}
                variant={
                  activeConversationId === conversation.id
                    ? "secondary"
                    : "ghost"
                }
                onClick={() => setActiveConversation(conversation.id)}
                className="w-full justify-start text-left h-auto p-3"
              >
                <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{conversation.title}</span>
              </Button>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
