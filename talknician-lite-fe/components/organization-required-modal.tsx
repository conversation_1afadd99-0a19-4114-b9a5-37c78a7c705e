"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Building, Users, Plus, Search, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface OrganizationRequiredModalProps {
  open: boolean;
  onOrganizationCreated: () => void;
}

export function OrganizationRequiredModal({
  open,
  onOrganizationCreated,
}: OrganizationRequiredModalProps) {
  const [activeTab, setActiveTab] = useState("create");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Create organization form
  const [createForm, setCreateForm] = useState({
    name: "",
    slug: "",
  });

  // Join organization form
  const [joinForm, setJoinForm] = useState({
    slug: "",
  });

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/create-organization`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify(createForm),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create organization");
      }

      const data = await response.json();
      setSuccess("Organization created successfully! Redirecting...");

      // Wait a moment to show success message, then redirect
      setTimeout(() => {
        onOrganizationCreated();
      }, 1500);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/join-organization`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify(joinForm),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to join organization");
      }

      const data = await response.json();
      setSuccess("Successfully joined organization! Redirecting...");

      // Wait a moment to show success message, then redirect
      setTimeout(() => {
        onOrganizationCreated();
      }, 1500);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  };

  const handleNameChange = (name: string) => {
    setCreateForm({
      name,
      slug: generateSlug(name),
    });
  };

  return (
    <Dialog open={open}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building className="w-5 h-5 text-indigo-600" />
            <span>Organization Required</span>
          </DialogTitle>
          <DialogDescription>
            You need to be part of an organization to use Talknician. Create a
            new organization or join an existing one.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create" className="flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Create</span>
            </TabsTrigger>
            <TabsTrigger value="join" className="flex items-center space-x-2">
              <Search className="w-4 h-4" />
              <span>Join</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Create Organization</CardTitle>
                <CardDescription>
                  Start your own organization and invite team members later.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleCreateOrganization} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="orgName">Organization Name</Label>
                    <Input
                      id="orgName"
                      placeholder="Acme Corporation"
                      value={createForm.name}
                      onChange={(e) => handleNameChange(e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="orgSlug">Organization Slug</Label>
                    <Input
                      id="orgSlug"
                      placeholder="acme-corporation"
                      value={createForm.slug}
                      onChange={(e) =>
                        setCreateForm({ ...createForm, slug: e.target.value })
                      }
                      required
                      disabled={isLoading}
                    />
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      This will be used in your organization URL. Only letters,
                      numbers, and hyphens allowed.
                    </p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-indigo-600 hover:bg-indigo-700"
                    disabled={isLoading || !createForm.name || !createForm.slug}
                  >
                    {isLoading ? "Creating..." : "Create Organization"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="join" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Join Organization</CardTitle>
                <CardDescription>
                  Enter the organization slug to request access.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleJoinOrganization} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="joinSlug">Organization Slug</Label>
                    <Input
                      id="joinSlug"
                      placeholder="acme-corporation"
                      value={joinForm.slug}
                      onChange={(e) => setJoinForm({ slug: e.target.value })}
                      required
                      disabled={isLoading}
                    />
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Ask your team manager or owner for the organization slug.
                    </p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-indigo-600 hover:bg-indigo-700"
                    disabled={isLoading || !joinForm.slug}
                  >
                    {isLoading ? "Joining..." : "Join Organization"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-center pt-4 border-t border-slate-200 dark:border-slate-700">
          <div className="flex items-center space-x-2 text-sm text-slate-500 dark:text-slate-400">
            <Users className="w-4 h-4" />
            <span>
              You'll be able to invite team members after creating your
              organization
            </span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
