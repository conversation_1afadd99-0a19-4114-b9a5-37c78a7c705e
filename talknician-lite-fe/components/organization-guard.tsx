"use client";

import { useAuth } from "@/contexts/AuthContext";
import { OrganizationRequiredModal } from "./organization-required-modal";
import { useOrganizationCheck } from "@/hooks/useOrganizationCheck";

interface OrganizationGuardProps {
  children: React.ReactNode;
}

export function OrganizationGuard({ children }: OrganizationGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const { showModal, handleOrganizationCreated } = useOrganizationCheck();

  // Don't show modal if user is not authenticated or still loading
  if (!isAuthenticated || isLoading) {
    return <>{children}</>;
  }

  return (
    <>
      {children}
      <OrganizationRequiredModal
        open={showModal}
        onOrganizationCreated={handleOrganizationCreated}
      />
    </>
  );
}
