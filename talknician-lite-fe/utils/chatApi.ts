import { 
  Conversation, 
  ChatMessage, 
  ChatSettings,
  ConversationsResponse,
  ConversationResponse,
  CreateConversationResponse,
  ChatSettingsResponse
} from "@/types/chat";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export class ChatApiClient {
  constructor(private getAuthHeaders: () => Record<string, string>) {}

  // Conversations
  async getConversations(organizationId: string): Promise<Conversation[]> {
    const response = await fetch(
      `${API_BASE_URL}/api/chat/conversations?organizationId=${organizationId}`,
      { headers: this.getAuthHeaders() }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch conversations");
    }

    const data: ConversationsResponse = await response.json();
    return data.data.conversations;
  }

  async getConversation(conversationId: string): Promise<{ conversation: Conversation; messages: ChatMessage[] }> {
    const response = await fetch(
      `${API_BASE_URL}/api/chat/conversations/${conversationId}`,
      { headers: this.getAuthHeaders() }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch conversation");
    }

    const data: ConversationResponse = await response.json();
    return {
      conversation: data.data.conversation,
      messages: data.data.messages,
    };
  }

  async createConversation(title: string = "New Conversation"): Promise<Conversation> {
    const response = await fetch(`${API_BASE_URL}/api/chat/conversations`, {
      method: "POST",
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ title }),
    });

    if (!response.ok) {
      throw new Error("Failed to create conversation");
    }

    const data: CreateConversationResponse = await response.json();
    return data.data;
  }

  async deleteConversation(conversationId: string): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/api/chat/conversations/${conversationId}`,
      {
        method: "DELETE",
        headers: this.getAuthHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to delete conversation");
    }
  }

  // Chat Settings
  async getChatSettings(organizationId: string): Promise<ChatSettings> {
    const response = await fetch(
      `${API_BASE_URL}/api/chat/settings?organizationId=${organizationId}`,
      { headers: this.getAuthHeaders() }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch chat settings");
    }

    const data: ChatSettingsResponse = await response.json();
    return data.data;
  }

  async updateChatSettings(organizationId: string, settings: Partial<ChatSettings>): Promise<ChatSettings> {
    const response = await fetch(`${API_BASE_URL}/api/chat/settings`, {
      method: "PUT",
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ organizationId, ...settings }),
    });

    if (!response.ok) {
      throw new Error("Failed to update chat settings");
    }

    const data: ChatSettingsResponse = await response.json();
    return data.data;
  }
}

// Factory function to create API client with auth
export const createChatApiClient = (accessToken: string | null) => {
  return new ChatApiClient(() => ({
    "Content-Type": "application/json",
    ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
  }));
};
