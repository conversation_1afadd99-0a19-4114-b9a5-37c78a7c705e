import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

// Types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface Conversation {
  id: string;
  title: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  conversationId: string;
  createdAt: string;
}

export interface Document {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  status: "PROCESSING" | "COMPLETED" | "FAILED";
  organizationId: string;
  createdAt: string;
  openaiFileId?: string;
  inRAG?: boolean;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: string;
  createdAt: string;
}

export interface OneDriveItem {
  id: string;
  name: string;
  folder?: object;
  file?: object;
  size: number;
  lastModifiedDateTime: string;
  webUrl: string;
}

// API Client Class
class ApiClient {
  private baseUrl: string;
  private getHeaders: () => Record<string, string>;

  constructor(baseUrl: string, getHeaders: () => Record<string, string>) {
    this.baseUrl = baseUrl;
    this.getHeaders = getHeaders;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    let headers = {
      ...this.getHeaders(),
      ...options.headers,
    };

    // Only set Content-Type if not sending FormData
    if (!(options.body instanceof FormData)) {
      headers = {
        "Content-Type": "application/json",
        ...headers,
      };
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({
        message: "An error occurred",
      }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Conversations
  async getConversations(
    organizationId: string
  ): Promise<ApiResponse<Conversation[]>> {
    return this.request(`/api/conversations?organizationId=${organizationId}`);
  }

  async getConversation(
    id: string
  ): Promise<ApiResponse<Conversation & { messages: Message[] }>> {
    return this.request(`/api/conversations/${id}`);
  }

  async createConversation(data: {
    title: string;
    organizationId: string;
  }): Promise<ApiResponse<Conversation>> {
    return this.request("/api/conversations", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateConversation(
    id: string,
    data: Partial<Conversation>
  ): Promise<ApiResponse<Conversation>> {
    return this.request(`/api/conversations/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteConversation(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/conversations/${id}`, {
      method: "DELETE",
    });
  }

  // Messages
  async sendMessage(data: {
    content: string;
    conversationId: string;
  }): Promise<ApiResponse<Message>> {
    return this.request("/api/messages", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Documents
  async getDocuments(organizationId: string): Promise<ApiResponse<Document[]>> {
    return this.request(`/api/documents?organizationId=${organizationId}`);
  }

  async uploadDocument(
    file: File,
    organizationId: string
  ): Promise<ApiResponse<Document>> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("organizationId", organizationId);

    return this.request("/api/documents/upload", {
      method: "POST",
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    });
  }

  async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/documents/${id}`, {
      method: "DELETE",
    });
  }

  async addDocumentToRAG(id: string): Promise<ApiResponse<Document>> {
    return this.request(`/api/documents/${id}/add-to-rag`, {
      method: "POST",
    });
  }

  async removeDocumentFromRAG(id: string): Promise<ApiResponse<Document>> {
    return this.request(`/api/documents/${id}/remove-from-rag`, {
      method: "POST",
    });
  }

  async downloadDocument(
    id: string
  ): Promise<{ blob: Blob; filename: string }> {
    const url = `${this.baseUrl}/api/documents/${id}/download`;
    const response = await fetch(url, {
      method: "GET",
      headers: this.getHeaders(),
    });
    if (!response.ok) {
      throw new Error("Failed to download document");
    }
    // Try to get filename from Content-Disposition
    let filename = "document";
    const disposition = response.headers.get("Content-Disposition");
    if (disposition) {
      const match = disposition.match(/filename="(.+)"/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    const blob = await response.blob();
    return { blob, filename };
  }

  async renameDocument(
    id: string,
    originalName: string
  ): Promise<ApiResponse<{ id: string; originalName: string }>> {
    return this.request(`/api/documents/${id}`, {
      method: "PATCH",
      body: JSON.stringify({ originalName }),
    });
  }

  // Organizations
  async getOrganizations(): Promise<ApiResponse<Organization[]>> {
    return this.request("/api/organizations");
  }

  async createOrganization(data: {
    name: string;
    slug?: string;
  }): Promise<ApiResponse<Organization>> {
    return this.request("/api/organizations", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  getOneDriveAuthUrl(): string {
    return `${this.baseUrl}/api/integrations/onedrive/auth`;
  }

  async listOneDriveFiles(path: string = "/"): Promise<OneDriveItem[]> {
    const response = await fetch(
      `${this.baseUrl}/api/integrations/onedrive/list?path=${encodeURIComponent(
        path
      )}`,
      {
        credentials: "include",
        headers: this.getHeaders(),
      }
    );
    if (response.status === 401) throw new Error("Not connected");
    if (!response.ok) throw new Error("Failed to list OneDrive files");
    const data = await response.json();
    return data.data;
  }

  async isOneDriveConnected(): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/integrations/onedrive/status`,
        {
          credentials: "include",
          headers: this.getHeaders(),
        }
      );
      if (!response.ok) return false;
      const data = await response.json();
      return data.connected;
    } catch (e: any) {
      return false;
    }
  }

  async addOneDriveFileToRAG({
    fileId,
    fileName,
    organizationId,
  }: {
    fileId: string;
    fileName: string;
    organizationId: string;
  }): Promise<ApiResponse<Document>> {
    return this.request("/api/integrations/onedrive/add-to-rag", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
      body: JSON.stringify({ fileId, fileName, organizationId }),
    });
  }

  async disconnectOneDrive(): Promise<ApiResponse<void>> {
    const response = await fetch(
      `${this.baseUrl}/api/integrations/onedrive/disconnect`,
      {
        method: "POST",
        credentials: "include",
        headers: this.getHeaders(),
      }
    );
    if (!response.ok) {
      throw new Error("Failed to disconnect OneDrive");
    }
    return response.json();
  }
}

// Custom hook to get API client with auth
export const useApiClient = () => {
  const { accessToken } = useAuth();

  const apiClient = new ApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );

  return apiClient;
};

// Query Keys
export const queryKeys = {
  conversations: (orgId: string) => ["conversations", orgId] as const,
  conversation: (id: string) => ["conversation", id] as const,
  documents: (orgId: string) => ["documents", orgId] as const,
  organizations: () => ["organizations"] as const,
};

// React Query Hooks
export const useConversationsQuery = (organizationId: string) => {
  const apiClient = useApiClient();

  return useQuery({
    queryKey: queryKeys.conversations(organizationId),
    queryFn: () => apiClient.getConversations(organizationId),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useConversationQuery = (id: string) => {
  const apiClient = useApiClient();

  return useQuery({
    queryKey: queryKeys.conversation(id),
    queryFn: () => apiClient.getConversation(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useDocumentsQuery = (organizationId: string) => {
  const apiClient = useApiClient();

  return useQuery({
    queryKey: queryKeys.documents(organizationId),
    queryFn: () => apiClient.getDocuments(organizationId),
    enabled: !!organizationId,
    staleTime: 30 * 1000, // 30 seconds - shorter for better responsiveness
    refetchInterval: (data) => {
      // Poll every 3 seconds if there are any processing documents
      const hasProcessingDocs = data?.data?.some(
        (doc: Document) => doc.status === "PROCESSING"
      );
      return hasProcessingDocs ? 3000 : false;
    },
    refetchIntervalInBackground: true,
  });
};

export const useOrganizationsQuery = () => {
  const apiClient = useApiClient();

  return useQuery({
    queryKey: queryKeys.organizations(),
    queryFn: () => apiClient.getOrganizations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Mutations
export const useCreateConversationMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { title: string; organizationId: string }) =>
      apiClient.createConversation(data),
    onSuccess: (response, variables) => {
      // Invalidate conversations list
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversations(variables.organizationId),
      });
    },
  });
};

export const useSendMessageMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { content: string; conversationId: string }) =>
      apiClient.sendMessage(data),
    onSuccess: (response, variables) => {
      // Update conversation cache
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversation(variables.conversationId),
      });
    },
  });
};

export const useUploadDocumentMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      file,
      organizationId,
    }: {
      file: File;
      organizationId: string;
    }) => apiClient.uploadDocument(file, organizationId),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};

export const useDeleteConversationMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.deleteConversation(id),
    onSuccess: (response, id) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: queryKeys.conversation(id) });
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });
};

export const useDeleteDocumentMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.deleteDocument(id),
    onSuccess: () => {
      // Invalidate documents lists
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useAddDocumentToRAGMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.addDocumentToRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRemoveDocumentFromRAGMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.removeDocumentFromRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRenameDocumentMutation = () => {
  const apiClient = useApiClient();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      originalName,
      organizationId,
    }: {
      id: string;
      originalName: string;
      organizationId: string;
    }) => apiClient.renameDocument(id, originalName),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};
