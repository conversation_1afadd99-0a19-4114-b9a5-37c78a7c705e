import { Auth0Client, PopupLoginOptions } from "@auth0/auth0-spa-js";

export interface Auth0Config {
  domain: string;
  clientId: string;
  audience: string;
  redirectUri: string;
}

export const auth0Config: Auth0Config = {
  domain:
    process.env.NEXT_PUBLIC_AUTH0_DOMAIN || "dev-55elwor2inabc85m.us.auth0.com",
  clientId:
    process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID ||
    "YWQLOwYeSlTaMeNqbMQzt0kGqC5l1kJZ",
  audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE || "http://localhost:8000/",
  redirectUri: typeof window !== "undefined" ? window.location.origin : "",
};

let auth0Client: Auth0Client | null = null;

export const getAuth0Client = async (): Promise<Auth0Client> => {
  if (!auth0Client) {
    auth0Client = new Auth0Client({
      domain: auth0Config.domain,
      clientId: auth0Config.clientId,
      authorizationParams: {
        audience: auth0Config.audience,
        scope: "openid profile email",
        redirect_uri: auth0Config.redirectUri,
      },
      cacheLocation: "localstorage",
      useRefreshTokens: true,
    });
  }
  return auth0Client;
};

export interface SocialLoginOptions {
  connection: "google-oauth2" | "windowslive";
}

export const loginWithPopup = async (
  options: SocialLoginOptions
): Promise<{ user: any; token: string }> => {
  const auth0 = await getAuth0Client();

  const popupOptions: PopupLoginOptions = {
    authorizationParams: {
      connection: options.connection,
      audience: auth0Config.audience,
      scope: "openid profile email",
    },
  };

  try {
    // Perform popup login
    await auth0.loginWithPopup(popupOptions);

    // Get the user and token
    const user = await auth0.getUser();
    const token = await auth0.getTokenSilently();

    if (!user || !token) {
      throw new Error("Authentication failed");
    }

    return { user, token };
  } catch (error) {
    const authError = error as Error;
    throw authError;
  }
};

export const logout = async (): Promise<void> => {
  const auth0 = await getAuth0Client();
  auth0.logout({
    logoutParams: {
      returnTo: window.location.origin,
    },
  });
};

export const getUser = async () => {
  const auth0 = await getAuth0Client();
  return auth0.getUser();
};

export const getToken = async () => {
  const auth0 = await getAuth0Client();
  return auth0.getTokenSilently();
};

export const isAuthenticated = async (): Promise<boolean> => {
  const auth0 = await getAuth0Client();
  return auth0.isAuthenticated();
};
