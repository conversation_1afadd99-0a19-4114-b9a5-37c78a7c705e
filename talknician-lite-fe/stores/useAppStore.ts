import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Types
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: string;
}

interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  timestamp: Date;
  conversationId: string;
}

interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  organizationId: string;
  lastUpdated: Date;
}

interface Document {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  status: "PROCESSING" | "COMPLETED" | "FAILED";
  organizationId: string;
  createdAt: Date;
}

interface UIState {
  sidebarOpen: boolean;
  currentOrganization: Organization | null;
  activeConversation: string | null;
  isLoading: boolean;
  notifications: Notification[];
  modals: {
    createOrganization: boolean;
    uploadDocument: boolean;
    userProfile: boolean;
  };
}

interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  message: string;
  timestamp: Date;
  read: boolean;
}

// Store State
interface AppState {
  // Data
  conversations: Record<string, Conversation>;
  documents: Record<string, Document>;

  // UI State
  ui: UIState;

  // Actions - Conversations
  addConversation: (conversation: Conversation) => void;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  deleteConversation: (id: string) => void;
  addMessage: (conversationId: string, message: ChatMessage) => void;

  // Actions - Documents
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  deleteDocument: (id: string) => void;

  // Actions - UI
  setSidebarOpen: (open: boolean) => void;
  setCurrentOrganization: (org: Organization | null) => void;
  setActiveConversation: (id: string | null) => void;
  setLoading: (loading: boolean) => void;
  openModal: (modal: keyof UIState["modals"]) => void;
  closeModal: (modal: keyof UIState["modals"]) => void;
  closeAllModals: () => void;

  // Actions - Notifications
  addNotification: (
    notification: Omit<Notification, "id" | "timestamp" | "read">
  ) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;

  // Actions - Reset
  resetStore: () => void;
}

// Initial State
const initialUIState: UIState = {
  sidebarOpen: true,
  currentOrganization: null,
  activeConversation: null,
  isLoading: false,
  notifications: [],
  modals: {
    createOrganization: false,
    uploadDocument: false,
    userProfile: false,
  },
};

// Store
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        conversations: {},
        documents: {},
        ui: initialUIState,

        // Conversation Actions
        addConversation: (conversation) =>
          set(
            (state) => ({
              conversations: {
                ...state.conversations,
                [conversation.id]: conversation,
              },
            }),
            false,
            "addConversation"
          ),

        updateConversation: (id, updates) =>
          set(
            (state) => ({
              conversations: {
                ...state.conversations,
                [id]: { ...state.conversations[id], ...updates },
              },
            }),
            false,
            "updateConversation"
          ),

        deleteConversation: (id) =>
          set(
            (state) => {
              const { [id]: deleted, ...rest } = state.conversations;
              return { conversations: rest };
            },
            false,
            "deleteConversation"
          ),

        addMessage: (conversationId, message) =>
          set(
            (state) => {
              const conversation = state.conversations[conversationId];
              if (!conversation) return state;

              return {
                conversations: {
                  ...state.conversations,
                  [conversationId]: {
                    ...conversation,
                    messages: [...conversation.messages, message],
                    lastUpdated: new Date(),
                  },
                },
              };
            },
            false,
            "addMessage"
          ),

        // Document Actions
        addDocument: (document) =>
          set(
            (state) => ({
              documents: {
                ...state.documents,
                [document.id]: document,
              },
            }),
            false,
            "addDocument"
          ),

        updateDocument: (id, updates) =>
          set(
            (state) => ({
              documents: {
                ...state.documents,
                [id]: { ...state.documents[id], ...updates },
              },
            }),
            false,
            "updateDocument"
          ),

        deleteDocument: (id) =>
          set(
            (state) => {
              const { [id]: deleted, ...rest } = state.documents;
              return { documents: rest };
            },
            false,
            "deleteDocument"
          ),

        // UI Actions
        setSidebarOpen: (open) =>
          set(
            (state) => ({
              ui: { ...state.ui, sidebarOpen: open },
            }),
            false,
            "setSidebarOpen"
          ),

        setCurrentOrganization: (org) =>
          set(
            (state) => ({
              ui: { ...state.ui, currentOrganization: org },
            }),
            false,
            "setCurrentOrganization"
          ),

        setActiveConversation: (id) =>
          set(
            (state) => ({
              ui: { ...state.ui, activeConversation: id },
            }),
            false,
            "setActiveConversation"
          ),

        setLoading: (loading) =>
          set(
            (state) => ({
              ui: { ...state.ui, isLoading: loading },
            }),
            false,
            "setLoading"
          ),

        openModal: (modal) =>
          set(
            (state) => ({
              ui: {
                ...state.ui,
                modals: { ...state.ui.modals, [modal]: true },
              },
            }),
            false,
            "openModal"
          ),

        closeModal: (modal) =>
          set(
            (state) => ({
              ui: {
                ...state.ui,
                modals: { ...state.ui.modals, [modal]: false },
              },
            }),
            false,
            "closeModal"
          ),

        closeAllModals: () =>
          set(
            (state) => ({
              ui: {
                ...state.ui,
                modals: {
                  createOrganization: false,
                  uploadDocument: false,
                  userProfile: false,
                },
              },
            }),
            false,
            "closeAllModals"
          ),

        // Notification Actions
        addNotification: (notification) =>
          set(
            (state) => {
              const newNotification: Notification = {
                ...notification,
                id: Math.random().toString(36).substr(2, 9),
                timestamp: new Date(),
                read: false,
              };
              return {
                ui: {
                  ...state.ui,
                  notifications: [newNotification, ...state.ui.notifications],
                },
              };
            },
            false,
            "addNotification"
          ),

        markNotificationRead: (id) =>
          set(
            (state) => ({
              ui: {
                ...state.ui,
                notifications: state.ui.notifications.map((n) =>
                  n.id === id ? { ...n, read: true } : n
                ),
              },
            }),
            false,
            "markNotificationRead"
          ),

        clearNotifications: () =>
          set(
            (state) => ({
              ui: { ...state.ui, notifications: [] },
            }),
            false,
            "clearNotifications"
          ),

        // Reset
        resetStore: () =>
          set(
            {
              conversations: {},
              documents: {},
              ui: initialUIState,
            },
            false,
            "resetStore"
          ),
      }),
      {
        name: "talknician-app-store",
        // Only persist certain parts, not everything
        partialize: (state) => ({
          ui: {
            sidebarOpen: state.ui.sidebarOpen,
            currentOrganization: state.ui.currentOrganization,
          },
        }),
      }
    ),
    { name: "TalknicianStore" }
  )
);

// Selectors (for performance)
export const useConversations = () =>
  useAppStore((state) => state.conversations);
export const useDocuments = () => useAppStore((state) => state.documents);
export const useUI = () => useAppStore((state) => state.ui);
export const useNotifications = () =>
  useAppStore((state) => state.ui.notifications);
export const useCurrentOrganization = () =>
  useAppStore((state) => state.ui.currentOrganization);
export const useActiveConversation = () =>
  useAppStore((state) => state.ui.activeConversation);

// Action selectors
export const useAppActions = () =>
  useAppStore((state) => ({
    addConversation: state.addConversation,
    updateConversation: state.updateConversation,
    deleteConversation: state.deleteConversation,
    addMessage: state.addMessage,
    addDocument: state.addDocument,
    updateDocument: state.updateDocument,
    deleteDocument: state.deleteDocument,
    setSidebarOpen: state.setSidebarOpen,
    setCurrentOrganization: state.setCurrentOrganization,
    setActiveConversation: state.setActiveConversation,
    setLoading: state.setLoading,
    openModal: state.openModal,
    closeModal: state.closeModal,
    closeAllModals: state.closeAllModals,
    addNotification: state.addNotification,
    markNotificationRead: state.markNotificationRead,
    clearNotifications: state.clearNotifications,
    resetStore: state.resetStore,
  }));
