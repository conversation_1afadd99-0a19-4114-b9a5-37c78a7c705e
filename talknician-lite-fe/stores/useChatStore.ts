import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { ChatMessage, Conversation, ChatSettings, StreamingState } from "@/types/chat";

interface ChatState {
  // Current conversation and messages
  currentConversation: Conversation | null;
  messages: ChatMessage[];
  
  // Conversations list
  conversations: Conversation[];
  
  // UI state
  inputValue: string;
  conversationSearchQuery: string;
  isMobileConversationsOpen: boolean;
  
  // Chat settings
  chatSettings: ChatSettings;
  currentRequestWebSearch: boolean;
  
  // Streaming state
  streamingState: StreamingState;
  
  // Actions
  setCurrentConversation: (conversation: Conversation | null) => void;
  setMessages: (messages: ChatMessage[]) => void;
  addMessage: (message: ChatMessage) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  clearMessages: () => void;
  
  setConversations: (conversations: Conversation[]) => void;
  addConversation: (conversation: Conversation) => void;
  removeConversation: (conversationId: string) => void;
  
  setInputValue: (value: string) => void;
  setConversationSearchQuery: (query: string) => void;
  setIsMobileConversationsOpen: (open: boolean) => void;
  
  setChatSettings: (settings: ChatSettings) => void;
  setCurrentRequestWebSearch: (enabled: boolean) => void;
  
  setStreamingState: (state: Partial<StreamingState>) => void;
  
  // Reset store (when changing conversations)
  resetMessages: () => void;
  resetStore: () => void;
}

const initialStreamingState: StreamingState = {
  isStreaming: false,
  currentContent: "",
  error: null,
  status: "idle",
  loadingMessage: "",
};

const initialChatSettings: ChatSettings = {
  enableWebSearch: false,
  personality: "helpful",
  systemPrompt: "",
};

export const useChatStore = create<ChatState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentConversation: null,
      messages: [],
      conversations: [],
      inputValue: "",
      conversationSearchQuery: "",
      isMobileConversationsOpen: false,
      chatSettings: initialChatSettings,
      currentRequestWebSearch: false,
      streamingState: initialStreamingState,

      // Conversation actions
      setCurrentConversation: (conversation) =>
        set({ currentConversation: conversation }, false, "setCurrentConversation"),

      // Message actions
      setMessages: (messages) =>
        set({ messages }, false, "setMessages"),

      addMessage: (message) =>
        set(
          (state) => ({ messages: [...state.messages, message] }),
          false,
          "addMessage"
        ),

      updateMessage: (messageId, updates) =>
        set(
          (state) => ({
            messages: state.messages.map((msg) =>
              msg.id === messageId ? { ...msg, ...updates } : msg
            ),
          }),
          false,
          "updateMessage"
        ),

      clearMessages: () =>
        set({ messages: [] }, false, "clearMessages"),

      // Conversations actions
      setConversations: (conversations) =>
        set({ conversations }, false, "setConversations"),

      addConversation: (conversation) =>
        set(
          (state) => ({ conversations: [conversation, ...state.conversations] }),
          false,
          "addConversation"
        ),

      removeConversation: (conversationId) =>
        set(
          (state) => ({
            conversations: state.conversations.filter((c) => c.id !== conversationId),
            currentConversation:
              state.currentConversation?.id === conversationId
                ? null
                : state.currentConversation,
          }),
          false,
          "removeConversation"
        ),

      // UI actions
      setInputValue: (value) =>
        set({ inputValue: value }, false, "setInputValue"),

      setConversationSearchQuery: (query) =>
        set({ conversationSearchQuery: query }, false, "setConversationSearchQuery"),

      setIsMobileConversationsOpen: (open) =>
        set({ isMobileConversationsOpen: open }, false, "setIsMobileConversationsOpen"),

      // Settings actions
      setChatSettings: (settings) =>
        set({ chatSettings: settings }, false, "setChatSettings"),

      setCurrentRequestWebSearch: (enabled) =>
        set({ currentRequestWebSearch: enabled }, false, "setCurrentRequestWebSearch"),

      // Streaming actions
      setStreamingState: (state) =>
        set(
          (prevState) => ({
            streamingState: { ...prevState.streamingState, ...state },
          }),
          false,
          "setStreamingState"
        ),

      // Reset actions
      resetMessages: () =>
        set({ messages: [] }, false, "resetMessages"),

      resetStore: () =>
        set(
          {
            currentConversation: null,
            messages: [],
            conversations: [],
            inputValue: "",
            conversationSearchQuery: "",
            isMobileConversationsOpen: false,
            chatSettings: initialChatSettings,
            currentRequestWebSearch: false,
            streamingState: initialStreamingState,
          },
          false,
          "resetStore"
        ),
    }),
    { name: "ChatStore" }
  )
);

// Selectors for performance
export const useCurrentConversation = () => useChatStore((state) => state.currentConversation);
export const useMessages = () => useChatStore((state) => state.messages);
export const useConversations = () => useChatStore((state) => state.conversations);
export const useStreamingState = () => useChatStore((state) => state.streamingState);
export const useChatSettings = () => useChatStore((state) => state.chatSettings);
export const useInputValue = () => useChatStore((state) => state.inputValue);
