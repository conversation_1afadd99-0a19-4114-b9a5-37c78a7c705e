# Popup Authentication Setup Guide

## Overview

This guide shows you how to set up popup authentication for social logins (Google, Microsoft) using Auth0, eliminating the need for full page redirects.

## 1. Azure Redirect URL Configuration

For **Microsoft Login** through Auth0, set the redirect URL in your **Azure AD App Registration**:

```
https://dev-55elwor2inabc85m.us.auth0.com/login/callback
```

### Why this URL?

- Your app → Auth0 → Microsoft → Auth0 → Your app
- Microsoft redirects back to Auth0 (not your app directly)
- <PERSON><PERSON><PERSON> handles the response and manages the popup

## 2. Auth0 Dashboard Configuration

### Application Settings

1. Go to Auth0 Dashboard → Applications → Your App
2. **Allowed Callback URLs**:

   ```
   http://localhost:3000,
   https://yourdomain.com
   ```

3. **Allowed Web Origins**:

   ```
   http://localhost:3000,
   https://yourdomain.com
   ```

4. **Allowed Origins (CORS)**:
   ```
   http://localhost:3000,
   https://yourdomain.com
   ```

### Social Connections

1. Go to **Authentication → Social**
2. Enable **Google** and **Microsoft** connections
3. For Microsoft: Use the Azure redirect URL above

## 3. Environment Variables

Create `.env.local` in your frontend directory:

```env
# Auth0 Configuration
NEXT_PUBLIC_AUTH0_DOMAIN=dev-55elwor2inabc85m.us.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=48iarxFOApYHEvg2ZD0inMb7uxdKXVTB
NEXT_PUBLIC_AUTH0_AUDIENCE=https://talknician-api

# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## 4. How Popup Authentication Works

### Google Login Flow:

1. User clicks "Continue with Google"
2. Popup window opens with Google OAuth
3. User authenticates in popup
4. Popup closes automatically
5. User data returned to main window
6. Token stored in localStorage
7. User redirected to `/houston`

### Microsoft Login Flow:

1. User clicks "Continue with Microsoft"
2. Popup window opens with Microsoft OAuth
3. User authenticates in popup
4. Popup closes automatically
5. User data returned to main window
6. Token stored in localStorage
7. User redirected to `/houston`

## 5. Implementation Details

### Auth0 Configuration (`lib/auth0.ts`)

- Initializes Auth0 client with popup support
- Handles both Google (`google-oauth2`) and Microsoft (`windowslive`) connections
- Provides success/error callbacks
- Manages token storage

### Auth Context (`contexts/AuthContext.tsx`)

- Manages authentication state globally
- Provides login methods for credentials and social
- Handles token persistence
- Manages user session

### Login Page Updates

- Uses `useAuth()` hook for authentication methods
- Handles popup authentication with proper error handling
- Shows loading states during authentication
- Redirects to chat after successful login

## 6. Benefits of Popup Authentication

✅ **Better UX**: No page redirects, seamless experience
✅ **Faster**: No full page reloads
✅ **Mobile Friendly**: Works well on mobile devices
✅ **Context Preservation**: Maintains application state
✅ **Error Handling**: Better error management in popup

## 7. Testing

1. Start your backend: `cd talknician-lite-be && pnpm dev`
2. Start your frontend: `cd talknician-lite-fe && pnpm dev`
3. Go to `http://localhost:3000/login`
4. Try both Google and Microsoft popup logins
5. Verify tokens are stored and user is redirected

## 8. Troubleshooting

### Popup Blocked

- Ensure popup blockers are disabled for localhost
- Some browsers block popups not initiated by user action

### CORS Errors

- Check Auth0 **Allowed Origins (CORS)** settings
- Ensure your domain is listed

### Connection Errors

- Verify social connections are enabled in Auth0 Dashboard
- Check Azure AD app registration redirect URL

### Token Issues

- Check console for JWT verification errors
- Verify audience and domain configuration

## 9. Security Considerations

- Tokens are stored in localStorage (consider httpOnly cookies for production)
- Always validate tokens on your backend
- Use HTTPS in production
- Set appropriate token expiration times
- Implement proper logout functionality
