import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  HelpCircle,
  MessageSquare,
  Mail,
  Phone,
  Clock,
  Search,
  BookOpen,
  Video,
  Users,
  Zap,
  Shield,
  Settings,
} from "lucide-react"
import Link from "next/link"

export default function HelpSupportPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <HelpCircle className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-semibold text-slate-900 dark:text-white">Talknician</span>
          </Link>
          <Button asChild variant="outline">
            <Link href="/">Back to Home</Link>
          </Button>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-6">
            <HelpCircle className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
          </div>
          <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">Help & Support</h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8">
            Get the help you need to make the most of Talknician
          </p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input placeholder="Search for help articles..." className="pl-10 h-12 text-base" />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Live Chat</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Get instant help from our support team</p>
              <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
                Online Now
              </Badge>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Email Support</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Send us a detailed message</p>
              <Badge variant="outline">24h Response</Badge>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Documentation</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">Browse our comprehensive guides</p>
              <Badge variant="outline">Self-Service</Badge>
            </CardContent>
          </Card>
        </div>

        {/* Popular Topics */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">Popular Topics</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  <span>Getting Started</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → How to create your first chat
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Setting up integrations
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Understanding AI responses
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Managing your organization
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  <span>Account & Billing</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Managing your subscription
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Updating payment methods
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Team member management
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Account security settings
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  <span>Privacy & Security</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Data encryption and storage
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">→ GDPR compliance</li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Two-factor authentication
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Data export and deletion
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Video className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  <span>Advanced Features</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → API integration guide
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Custom AI model training
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Workflow automation
                  </li>
                  <li className="hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
                    → Enterprise deployment
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Contact Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
              <span>Contact Support</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <form className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        First Name
                      </label>
                      <Input placeholder="John" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Last Name
                      </label>
                      <Input placeholder="Doe" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Email</label>
                    <Input type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Subject</label>
                    <Input placeholder="How can we help you?" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Message</label>
                    <Textarea placeholder="Please describe your issue or question in detail..." rows={4} />
                  </div>
                  <Button className="w-full">Send Message</Button>
                </form>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-4">Other Ways to Reach Us</h3>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                        <Mail className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900 dark:text-white">Email</p>
                        <p className="text-slate-600 dark:text-slate-300"><EMAIL></p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                        <Phone className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900 dark:text-white">Phone</p>
                        <p className="text-slate-600 dark:text-slate-300">+****************</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                        <Clock className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900 dark:text-white">Support Hours</p>
                        <p className="text-slate-600 dark:text-slate-300">Mon-Fri, 9AM-6PM EST</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Users className="w-5 h-5 text-indigo-600 dark:text-indigo-400 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-1">Enterprise Support</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-300">
                        Need priority support? Contact our enterprise team for dedicated assistance.
                      </p>
                      <Button variant="outline" size="sm" className="mt-2">
                        Contact Enterprise
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator className="my-12" />

        <div className="text-center">
          <p className="text-slate-500 dark:text-slate-400 mb-4">
            Can't find what you're looking for? We're here to help!
          </p>
          <Button asChild>
            <Link href="/">Return to Home</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
