"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  MessageSquare,
  Loader2,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";

function VerifyEmailContent() {
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [canResend, setCanResend] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");
  const email = searchParams.get("email");

  useEffect(() => {
    if (!token) {
      setError("Invalid verification link");
      setIsLoading(false);
      setCanResend(true);
      return;
    }

    verifyEmail(token);
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/verify-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token: verificationToken }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setIsVerified(true);
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push("/login?verified=true");
        }, 3000);
      } else {
        setError(data.message || "Email verification failed");
        setCanResend(true);
      }
    } catch (error) {
      setError("Network error. Please try again.");
      setCanResend(true);
    } finally {
      setIsLoading(false);
    }
  };

  const resendVerificationEmail = async () => {
    if (!email) {
      setError("Email address not found");
      return;
    }

    setResendLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/resend-verification`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        alert("Verification email sent! Please check your inbox.");
      } else {
        setError(data.message || "Failed to resend verification email");
      }
    } catch (error) {
      setError("Network error. Please try again.");
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Theme Toggle */}
        <div className="flex justify-end">
          <ThemeToggle />
        </div>

        {/* Header */}
        <div className="text-center space-y-4">
          <Link href="/" className="inline-flex items-center space-x-2">
            <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold text-slate-900 dark:text-white">
              Talknician
            </span>
          </Link>
          <p className="text-slate-600 dark:text-slate-300 mt-2">
            Email Verification
          </p>
        </div>

        {/* Verification Card */}
        <Card className="border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-semibold text-slate-900 dark:text-white">
              {isLoading
                ? "Verifying Email..."
                : isVerified
                ? "Email Verified!"
                : "Verification Failed"}
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-300">
              {isLoading
                ? "Please wait while we verify your email address"
                : isVerified
                ? "Your email has been successfully verified"
                : "There was an issue verifying your email address"}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center py-8">
                <Loader2 className="w-8 h-8 animate-spin text-indigo-600" />
              </div>
            )}

            {/* Success State */}
            {isVerified && (
              <div className="text-center py-4">
                <div className="flex justify-center mb-4">
                  <CheckCircle className="w-16 h-16 text-green-600 dark:text-green-400" />
                </div>
                <p className="text-slate-600 dark:text-slate-300 mb-4">
                  Your email has been verified successfully! You can now sign in
                  to your account.
                </p>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Redirecting to login page in 3 seconds...
                </p>
              </div>
            )}

            {/* Error State */}
            {error && (
              <>
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>

                <div className="text-center py-4">
                  <div className="flex justify-center mb-4">
                    <AlertCircle className="w-16 h-16 text-red-600 dark:text-red-400" />
                  </div>

                  {canResend && email && (
                    <div className="space-y-3">
                      <p className="text-slate-600 dark:text-slate-300">
                        Didn't receive the verification email?
                      </p>
                      <Button
                        onClick={resendVerificationEmail}
                        disabled={resendLoading}
                        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                      >
                        {resendLoading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          "Resend Verification Email"
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Actions */}
            <div className="pt-4 space-y-3">
              <Button
                asChild
                variant="outline"
                className="w-full border-slate-200 dark:border-slate-600"
              >
                <Link href="/login">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Login
                </Link>
              </Button>

              {!isVerified && (
                <Button
                  asChild
                  variant="outline"
                  className="w-full border-slate-200 dark:border-slate-600"
                >
                  <Link href="/register">Create New Account</Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <p className="text-sm text-slate-500 dark:text-slate-400">
            Need help?{" "}
            <Link
              href="/support"
              className="text-indigo-600 dark:text-indigo-400 hover:underline"
            >
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center">
          <Loader2 className="w-8 h-8 animate-spin text-indigo-600" />
        </div>
      }
    >
      <VerifyEmailContent />
    </Suspense>
  );
}
