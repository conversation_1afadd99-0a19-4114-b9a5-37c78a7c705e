"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Zap,
  Shield,
  ArrowRight,
  Brain,
  Sparkles,
  CheckCircle,
  Globe,
  Lock,
} from "lucide-react";
import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";
import { UserProfile } from "@/components/user-profile";
import { useAuth } from "@/contexts/AuthContext";

export default function LandingPage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-semibold text-slate-900 dark:text-white">
              Talknician
            </span>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            {!isLoading && (
              <>
                {isAuthenticated ? (
                  <UserProfile showName={false} />
                ) : (
                  <Button
                    asChild
                    variant="ghost"
                    size="sm"
                    className="text-slate-600 dark:text-slate-300"
                  >
                    <Link href="/login">Login</Link>
                  </Button>
                )}
              </>
            )}
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="#features"
              className="text-slate-600 dark:text-slate-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
            >
              Features
            </Link>
            <Link
              href="#benefits"
              className="text-slate-600 dark:text-slate-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
            >
              Benefits
            </Link>
            {!isLoading && (
              <>
                {isAuthenticated ? (
                  <UserProfile />
                ) : (
                  <Link
                    href="/login"
                    className="text-slate-600 dark:text-slate-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Login
                  </Link>
                )}
              </>
            )}
            <ThemeToggle />
            <Button
              asChild
              className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg"
            >
              <Link href={isAuthenticated ? "/houston" : "/login"}>
                {isAuthenticated ? "Go to Chat" : "Get Started"}
              </Link>
            </Button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <Badge
            variant="outline"
            className="mb-6 border-indigo-200 dark:border-indigo-800 text-indigo-700 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-950"
          >
            ✨ Powered by Advanced AI
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6 leading-tight">
            Chat Smarter with{" "}
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent">
              Talknician
            </span>
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
            Experience the future of conversation with our intelligent AI
            assistant. Upload documents, search the web, and get instant,
            accurate responses with professional-grade AI technology.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg text-lg px-8 py-3"
            >
              <Link href={isAuthenticated ? "/houston" : "/login"}>
                {isAuthenticated ? (
                  <>
                    Continue Chatting <ArrowRight className="ml-2 w-5 h-5" />
                  </>
                ) : (
                  <>
                    Start Chatting <ArrowRight className="ml-2 w-5 h-5" />
                  </>
                )}
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="text-lg px-8 py-3 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
            >
              <Link href="#features">Learn More</Link>
            </Button>
          </div>

          {/* Welcome message for authenticated users */}
          {isAuthenticated && user && (
            <div className="mt-8 text-center">
              <p className="text-lg text-slate-600 dark:text-slate-300">
                Welcome back,{" "}
                <span className="font-semibold text-indigo-600 dark:text-indigo-400">
                  {user.name}
                </span>
                !
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-white dark:bg-slate-900">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Powerful Features
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300">
              Everything you need for intelligent conversations
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-slate-200 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 bg-white dark:bg-slate-800">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Smart Conversations
                </h3>
                <p className="text-slate-600 dark:text-slate-300">
                  Engage in natural, context-aware conversations with our
                  advanced AI assistant.
                </p>
              </CardContent>
            </Card>
            <Card className="border-slate-200 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 bg-white dark:bg-slate-800">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Zap className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Lightning Fast
                </h3>
                <p className="text-slate-600 dark:text-slate-300">
                  Get instant responses with our optimized AI infrastructure and
                  real-time processing.
                </p>
              </CardContent>
            </Card>
            <Card className="border-slate-200 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 bg-white dark:bg-slate-800">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Shield className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Secure & Private
                </h3>
                <p className="text-slate-600 dark:text-slate-300">
                  Your conversations are encrypted and protected with
                  enterprise-grade security.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section
        id="benefits"
        className="py-20 px-4 bg-gradient-to-br from-slate-50 to-indigo-50 dark:from-slate-800 dark:to-slate-900"
      >
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Why Choose Talknician?
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300">
              Discover what makes us different
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    Professional Grade AI
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    Built for professionals who demand accuracy, reliability,
                    and advanced capabilities in their AI interactions.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Globe className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    Web Integration
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    Seamlessly search the web and integrate real-time
                    information into your conversations.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Lock className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    Enterprise Security
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    Your data is protected with bank-level encryption and
                    privacy controls you can trust.
                  </p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl p-8 text-white">
                <div className="flex items-center space-x-3 mb-6">
                  <Sparkles className="w-8 h-8" />
                  <h3 className="text-2xl font-bold">
                    Ready to Transform Your Workflow?
                  </h3>
                </div>
                <p className="text-indigo-100 mb-6">
                  Join professionals who are already transforming their
                  productivity with Talknician's AI-powered conversations.
                </p>
                <div className="flex items-center space-x-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <p className="text-sm text-indigo-100">
                      Join our community
                    </p>
                    <p className="text-xs text-indigo-200">
                      Built for professionals
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700 py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-semibold text-slate-900 dark:text-white">
                  Talknician
                </span>
              </div>
              <p className="text-slate-600 dark:text-slate-300">
                The future of intelligent conversation, powered by advanced AI
                technology.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 dark:text-white mb-4">
                Product
              </h4>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>
                  <Link
                    href="/houston"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Chat
                  </Link>
                </li>
                <li>
                  <Link
                    href="/integrations"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Integrations
                  </Link>
                </li>
                <li>
                  <Link
                    href="#features"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 dark:text-white mb-4">
                Company
              </h4>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>
                  <Link
                    href="/about"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Contact
                  </Link>
                </li>
                <li>
                  <Link
                    href="/careers"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Careers
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 dark:text-white mb-4">
                Legal
              </h4>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>
                  <Link
                    href="/privacy"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href="/security"
                    className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    Security
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-200 dark:border-slate-700 mt-8 pt-8 text-center text-slate-600 dark:text-slate-300">
            <p>&copy; 2024 Talknician. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
