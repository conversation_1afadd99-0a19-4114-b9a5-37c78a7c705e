"use client";

import { useState, useEffect } from "react";
import { useOrganization } from "@/contexts/OrganizationContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  UserPlus,
  Mail,
  MoreVertical,
  Crown,
  Shield,
  User,
  Search,
  Building,
  Settings,
  Trash2,
  Edit,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Role constants for better maintainability
const ROLE = {
  OWNER: "owner",
  MANAGER: "manager", // Changed from admin
  MEMBER: "member",
} as const;

type UserRole = (typeof ROLE)[keyof typeof ROLE];

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  joinedAt: Date;
  lastActive: Date;
  status: "active" | "invited" | "inactive";
}

interface Organization {
  id: string;
  name: string;
  plan: string;
  memberCount: number;
  createdAt: Date;
}

export default function OrganizationPage() {
  const { currentOrganization } = useOrganization();
  const [searchQuery, setSearchQuery] = useState("");
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<UserRole>(ROLE.MEMBER);
  const [inviteLoading, setInviteLoading] = useState(false);
  const [inviteError, setInviteError] = useState("");
  const [inviteSuccess, setInviteSuccess] = useState("");
  const [detailedOrgData, setDetailedOrgData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Edit role state
  const [editRoleDialog, setEditRoleDialog] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [newRole, setNewRole] = useState<UserRole>(ROLE.MEMBER);
  const [editRoleLoading, setEditRoleLoading] = useState(false);
  const [editRoleError, setEditRoleError] = useState("");

  // Resend invite state
  const [resendInviteLoading, setResendInviteLoading] = useState<string | null>(
    null
  );

  // Remove member state
  const [removeDialog, setRemoveDialog] = useState(false);
  const [removingMember, setRemovingMember] = useState<TeamMember | null>(null);
  const [removeLoading, setRemoveLoading] = useState(false);

  // Transfer ownership state
  const [transferOwnershipDialog, setTransferOwnershipDialog] = useState(false);
  const [selectedNewOwner, setSelectedNewOwner] = useState<string>("");
  const [transferLoading, setTransferLoading] = useState(false);
  const [transferError, setTransferError] = useState("");

  // Load organization data
  useEffect(() => {
    if (currentOrganization) {
      fetchOrganizationDetails();
    }
  }, [currentOrganization]);

  const fetchOrganizationDetails = async () => {
    if (!currentOrganization) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setDetailedOrgData(data.data.organization);
      }
    } catch (error) {
      console.error("Failed to fetch organization details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const organization = detailedOrgData || {
    id: currentOrganization?.id || "",
    name: currentOrganization?.name || "",
    plan: currentOrganization?.plan || "FREE",
    memberCount: 0,
    createdAt: new Date(),
  };

  const teamMembers =
    detailedOrgData?.members?.map(
      (member: {
        id: string;
        role: string;
        joinedAt: string;
        user: {
          id: string;
          name: string;
          email: string;
          avatar?: string;
        };
      }) => ({
        id: member.id,
        name: member.user.name || "Unknown",
        email: member.user.email,
        role: (member.role.toLowerCase() === "owner"
          ? ROLE.OWNER
          : member.role.toLowerCase() === "manager"
          ? ROLE.MANAGER
          : ROLE.MEMBER) as UserRole,
        avatar: member.user.avatar,
        joinedAt: new Date(member.joinedAt),
        lastActive: new Date(), // This would come from a separate endpoint
        status: "active" as const,
      })
    ) || [];

  const getRoleIcon = (role: string) => {
    switch (role) {
      case ROLE.OWNER:
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case ROLE.MANAGER:
        return <Shield className="w-4 h-4 text-blue-500" />;
      default:
        return <User className="w-4 h-4 text-slate-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "owner":
        return (
          <Badge
            variant="outline"
            className="border-yellow-200 text-yellow-700 bg-yellow-50"
          >
            Owner
          </Badge>
        );
      case "manager":
        return (
          <Badge
            variant="outline"
            className="border-blue-200 text-blue-700 bg-blue-50"
          >
            Manager
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-slate-200 text-slate-700 bg-slate-50"
          >
            Member
          </Badge>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge
            variant="outline"
            className="border-green-200 text-green-700 bg-green-50"
          >
            Active
          </Badge>
        );
      case "invited":
        return (
          <Badge
            variant="outline"
            className="border-orange-200 text-orange-700 bg-orange-50"
          >
            Invited
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-slate-200 text-slate-700 bg-slate-50"
          >
            Inactive
          </Badge>
        );
    }
  };

  const formatDate = (date: Date) => {
    return date + "";
  };

  const formatLastActive = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleInviteMember = async () => {
    if (!currentOrganization || !inviteEmail.trim()) return;

    setInviteLoading(true);
    setInviteError("");
    setInviteSuccess("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}/invite`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            email: inviteEmail.trim(),
            role: inviteRole === "manager" ? "MANAGER" : "MEMBER", // Backend expects MEMBER/MANAGER
          }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to invite member");
      }

      setInviteSuccess(`Successfully invited ${inviteEmail}!`);
      setInviteEmail("");
      setInviteRole("member");

      // Refresh organization details to show new member
      setTimeout(() => {
        fetchOrganizationDetails();
        setInviteSuccess("");
      }, 2000);
    } catch (error: any) {
      setInviteError(error.message || "Failed to invite member");
    } finally {
      setInviteLoading(false);
    }
  };

  const handleEditRole = (member: TeamMember) => {
    setEditingMember(member);
    setNewRole(member.role);
    setEditRoleDialog(true);
    setEditRoleError("");
  };

  const handleUpdateRole = async () => {
    if (!currentOrganization || !editingMember) return;

    setEditRoleLoading(true);
    setEditRoleError("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}/members/${editingMember.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            role:
              newRole === "owner"
                ? "OWNER"
                : newRole === "manager"
                ? "MANAGER"
                : "MEMBER", // Backend expects MEMBER/MANAGER/OWNER
          }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update role");
      }

      setEditRoleDialog(false);
      fetchOrganizationDetails(); // Refresh data
    } catch (error: any) {
      setEditRoleError(error.message || "Failed to update role");
    } finally {
      setEditRoleLoading(false);
    }
  };

  const handleResendInvite = async (member: TeamMember) => {
    if (!currentOrganization) return;

    setResendInviteLoading(member.id);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}/invite`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            email: member.email,
            role:
              member.role === "owner"
                ? "OWNER"
                : member.role === "manager"
                ? "MANAGER"
                : "MEMBER",
          }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to resend invite");
      }

      // Show success message briefly
      alert(`Invitation resent to ${member.email}`);
    } catch (error: any) {
      alert(error.message || "Failed to resend invite");
    } finally {
      setResendInviteLoading(null);
    }
  };

  const handleRemoveMember = (member: TeamMember) => {
    setRemovingMember(member);
    setRemoveDialog(true);
  };

  const confirmRemoveMember = async () => {
    if (!currentOrganization || !removingMember) return;

    setRemoveLoading(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}/members/${removingMember.id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to remove member");
      }

      setRemoveDialog(false);
      fetchOrganizationDetails(); // Refresh data
    } catch (error: any) {
      alert(error.message || "Failed to remove member");
    } finally {
      setRemoveLoading(false);
    }
  };

  const handleTransferOwnership = () => {
    setTransferOwnershipDialog(true);
    setTransferError("");
    setSelectedNewOwner("");
  };

  const confirmTransferOwnership = async () => {
    if (!currentOrganization || !selectedNewOwner) return;

    setTransferLoading(true);
    setTransferError("");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${currentOrganization.id}/transfer-ownership`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            newOwnerId: selectedNewOwner,
          }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to transfer ownership");
      }

      setTransferOwnershipDialog(false);
      fetchOrganizationDetails(); // Refresh data
      alert("Ownership transferred successfully!");
    } catch (error: any) {
      setTransferError(error.message || "Failed to transfer ownership");
    } finally {
      setTransferLoading(false);
    }
  };

  // Get eligible members for ownership transfer (managers only)
  const getEligibleMembers = () => {
    return teamMembers.filter(
      (member: TeamMember) =>
        member.role === ROLE.MANAGER && member.status === "active"
    );
  };

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-slate-500 dark:text-slate-400">
          No organization selected
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-slate-50 dark:bg-slate-900">
      {/* Header */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
              <Building className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                {organization.name}
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                {organization.plan} Plan • {organization.memberCount} members
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Invite Member
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite Team Member</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 pt-4">
                  {inviteError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-600">{inviteError}</p>
                    </div>
                  )}
                  {inviteSuccess && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-600">{inviteSuccess}</p>
                    </div>
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Select
                      value={inviteRole}
                      onValueChange={(value: UserRole) =>
                        setInviteRole(value as UserRole)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="owner">Owner</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button
                    onClick={handleInviteMember}
                    disabled={inviteLoading || !inviteEmail.trim()}
                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50"
                  >
                    {inviteLoading ? (
                      <>
                        <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Send Invitation
                      </>
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search team members..."
            className="pl-10 border-slate-200 focus:border-indigo-300"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 space-y-6 overflow-y-auto">
        <Tabs defaultValue="members" className="h-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="members">Team Members</TabsTrigger>
            <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
            <TabsTrigger value="settings">Organization Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="members" className="mt-6">
            <div className="grid gap-4">
              {teamMembers.map((member: TeamMember) => (
                <Card
                  key={member.id}
                  className="border-slate-200 dark:border-slate-700"
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Avatar className="w-12 h-12">
                          <AvatarImage
                            src={member.avatar || "/placeholder.svg"}
                            alt={member.name}
                          />
                          <AvatarFallback>
                            {member.name
                              .split(" ")
                              .map((n: string) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-slate-900 dark:text-white">
                              {member.name}
                            </h3>
                            {getRoleIcon(member.role)}
                          </div>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {member.email}
                          </p>
                          <div className="flex items-center space-x-4 mt-1">
                            <span className="text-xs text-slate-500 dark:text-slate-400">
                              Joined {formatDate(member.joinedAt)}
                            </span>
                            {member.status === "active" && (
                              <span className="text-xs text-slate-500 dark:text-slate-400">
                                Active {formatLastActive(member.lastActive)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getRoleBadge(member.role)}
                        {getStatusBadge(member.status)}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuPortal>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleEditRole(member)}
                              >
                                <Edit className="w-4 h-4 mr-2" />
                                Edit Role
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleResendInvite(member)}
                                disabled={resendInviteLoading === member.id}
                              >
                                <Mail className="w-4 h-4 mr-2" />
                                {resendInviteLoading === member.id
                                  ? "Sending..."
                                  : "Resend Invite"}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleRemoveMember(member)}
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Remove Member
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenuPortal>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="roles" className="mt-6">
            <div className="grid gap-6">
              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle>Role Permissions</CardTitle>
                  <p className="text-slate-600 dark:text-slate-400">
                    Manage what each role can do in your organization
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <Crown className="w-6 h-6 text-yellow-500 mt-1" />
                      <div className="flex-1">
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          Owner
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          Full access to all features, can manage organization
                          settings, billing, and all team members. Organization
                          creator with ultimate control.
                        </p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            Manage Organization
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Billing Access
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            User Management
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            All Integrations
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Delete Organization
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Shield className="w-6 h-6 text-blue-500 mt-1" />
                      <div className="flex-1">
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          Manager
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          Can manage team members, access integrations, and
                          configure organization features.
                        </p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            User Management
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Integrations
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Houston Access
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <User className="w-6 h-6 text-slate-500 mt-1" />
                      <div className="flex-1">
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          Member
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          Standard access to Houston AI assistant and basic
                          integrations.
                        </p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            Houston Access
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            File Upload
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <div className="grid gap-6">
              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle>Organization Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="org-name">Organization Name</Label>
                      <Input
                        id="org-name"
                        value={organization.name}
                        onChange={() => {}} // Read-only for now
                        readOnly
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="org-plan">Current Plan</Label>
                      <Input
                        id="org-plan"
                        value={organization.plan}
                        readOnly
                        className="mt-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="org-created">Created</Label>
                    <Input
                      id="org-created"
                      value={formatDate(organization.createdAt)}
                      readOnly
                      className="mt-1"
                    />
                  </div>
                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                    Update Organization
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle>Transfer Ownership</CardTitle>
                  <p className="text-slate-600 dark:text-slate-400">
                    Transfer ownership of this organization to another manager
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border border-orange-200 dark:border-orange-800 rounded-lg">
                    <h4 className="font-medium text-slate-900 dark:text-white mb-2">
                      Transfer Organization Ownership
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                      Transfer ownership of this organization to another manager
                      user. You will become a manager after the transfer is
                      complete.
                    </p>
                    <Button
                      onClick={handleTransferOwnership}
                      variant="outline"
                      className="border-orange-200 text-orange-600 hover:bg-orange-50"
                      disabled={getEligibleMembers().length === 0}
                    >
                      Transfer Ownership
                    </Button>
                    {getEligibleMembers().length === 0 && (
                      <p className="text-xs text-slate-500 mt-2">
                        No eligible managers found. You need at least one active
                        manager to transfer ownership.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle className="text-red-600">Danger Zone</CardTitle>
                  <p className="text-slate-600 dark:text-slate-400">
                    Irreversible and destructive actions
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg">
                    <h4 className="font-medium text-slate-900 dark:text-white mb-2">
                      Delete Organization
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                      Once you delete an organization, there is no going back.
                      Please be certain.
                    </p>
                    <Button
                      variant="outline"
                      className="border-red-200 text-red-600 hover:bg-red-600 hover:text-white"
                    >
                      Delete Organization
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Role Dialog */}
      <Dialog open={editRoleDialog} onOpenChange={setEditRoleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 pt-4">
            {editRoleError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{editRoleError}</p>
              </div>
            )}
            {editingMember && (
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage
                      src={editingMember.avatar || "/placeholder.svg"}
                      alt={editingMember.name}
                    />
                    <AvatarFallback>
                      {editingMember.name
                        .split(" ")
                        .map((n: string) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-slate-900 dark:text-white">
                      {editingMember.name}
                    </p>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      {editingMember.email}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">New Role</Label>
                  <Select
                    value={newRole}
                    onValueChange={(value: UserRole) =>
                      setNewRole(value as UserRole)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="member">Member</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex space-x-2 pt-2">
                  <Button
                    onClick={handleUpdateRole}
                    disabled={editRoleLoading || newRole === editingMember.role}
                    className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50"
                  >
                    {editRoleLoading ? (
                      <>
                        <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        Updating...
                      </>
                    ) : (
                      "Update Role"
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setEditRoleDialog(false)}
                    disabled={editRoleLoading}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <AlertDialog open={removeDialog} onOpenChange={setRemoveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              {removingMember && (
                <>
                  Are you sure you want to remove{" "}
                  <strong>{removingMember.name}</strong> from your organization?
                  This action cannot be undone and they will lose access to all
                  organization resources.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={removeLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveMember}
              disabled={removeLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {removeLoading ? (
                <>
                  <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Removing...
                </>
              ) : (
                "Remove Member"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Transfer Ownership Dialog */}
      <Dialog
        open={transferOwnershipDialog}
        onOpenChange={setTransferOwnershipDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Transfer Ownership</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 pt-4">
            {transferError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{transferError}</p>
              </div>
            )}
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-start space-x-2">
                  <Crown className="w-5 h-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      Important Notice
                    </p>
                    <p className="text-sm text-yellow-700 mt-1">
                      After transferring ownership, you will become a manager.
                      The new owner will have full control over the
                      organization, including the ability to remove you as a
                      member.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-owner">Select New Owner</Label>
                <Select
                  value={selectedNewOwner}
                  onValueChange={setSelectedNewOwner}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a manager to transfer ownership to" />
                  </SelectTrigger>
                  <SelectContent>
                    {getEligibleMembers().map((member: TeamMember) => (
                      <SelectItem key={member.id} value={member.id}>
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage
                              src={member.avatar || "/placeholder.svg"}
                              alt={member.name}
                            />
                            <AvatarFallback className="text-xs">
                              {member.name
                                .split(" ")
                                .map((n: string) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="font-medium">{member.name}</span>
                            <span className="text-slate-500 ml-2">
                              ({member.email})
                            </span>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button
                  onClick={confirmTransferOwnership}
                  disabled={transferLoading || !selectedNewOwner}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
                >
                  {transferLoading ? (
                    <>
                      <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Transferring...
                    </>
                  ) : (
                    "Transfer Ownership"
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setTransferOwnershipDialog(false)}
                  disabled={transferLoading}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
