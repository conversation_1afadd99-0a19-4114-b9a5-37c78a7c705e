"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Upload,
  FolderPlus,
  Trash2,
  MoreVertical,
  FileText,
  ImageIcon,
  File,
  Folder,
  Grid3X3,
  List,
  Download,
  Share,
  Edit,
  Cloud,
  HardDrive,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  Globe,
  Plus,
  ExternalLink,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import {
  useDocumentsQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useAddDocumentToRAGMutation,
  useRemoveDocumentFromRAGMutation,
  useApiClient,
  useRenameDocumentMutation,
  type Document,
  OneDriveItem,
} from "@/lib/api-client";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Dialog as ConfirmDialog,
  DialogContent as ConfirmDialogContent,
  DialogHeader as ConfirmDialogHeader,
  DialogFooter as ConfirmDialogFooter,
  DialogTitle as ConfirmDialogTitle,
  DialogDescription as ConfirmDialogDescription,
} from "@/components/ui/dialog";

interface CloudIntegration {
  id: string;
  name: string;
  type: "onedrive" | "googledrive";
  connected: boolean;
  filesCount?: number;
  lastSync?: Date;
}

export default function IntegrationsPage() {
  const { currentOrganization } = useOrganization();
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current tab from URL or default to "files"
  const currentTab = searchParams.get("tab") || "files";

  // Handle tab change with URL update
  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("tab", value);
    router.push(`/integrations?${params.toString()}`);
  };

  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  const [searchQuery, setSearchQuery] = useState("");
  const [uploadingFiles, setUploadingFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [renameFile, setRenameFile] = useState<Document | null>(null);
  const [renameValue, setRenameValue] = useState("");
  const [renameError, setRenameError] = useState("");

  // OneDrive integration state
  const [oneDriveConnected, setOneDriveConnected] = useState<boolean | null>(
    null
  );
  const [oneDriveLoading, setOneDriveLoading] = useState(false);
  const [oneDriveConnecting, setOneDriveConnecting] = useState(false);
  const [oneDriveError, setOneDriveError] = useState("");
  const [oneDrivePath, setOneDrivePath] = useState("/");
  const [oneDriveItems, setOneDriveItems] = useState<OneDriveItem[]>([]);
  const [oneDriveHistory, setOneDriveHistory] = useState<string[]>(["/"]);

  // OneDrive folder cache: path -> OneDriveItem[]
  const oneDriveCache = useRef<{ [path: string]: OneDriveItem[] }>({});

  // Fetch documents for current organization
  const { data: documentsResponse, isLoading: documentsLoading } =
    useDocumentsQuery(currentOrganization?.id || "");

  const uploadMutation = useUploadDocumentMutation();
  const deleteMutation = useDeleteDocumentMutation();
  const addToRAGMutation = useAddDocumentToRAGMutation();
  const removeFromRAGMutation = useRemoveDocumentFromRAGMutation();

  const documents = documentsResponse?.data || [];

  // Debug: Log documents and their inRAG status
  useEffect(() => {
    if (documents.length > 0) {
      console.log(
        "Documents:",
        documents.map((d) => ({
          name: d.originalName,
          inRAG: d.inRAG,
          openaiFileId: d.openaiFileId,
          status: d.status,
        }))
      );
      console.log("Documents in RAG:", documents.filter((f) => f.inRAG).length);
    }
  }, [documents]);

  const [cloudIntegrations] = useState<CloudIntegration[]>([
    {
      id: "2",
      name: "Google Drive",
      type: "googledrive",
      connected: false,
    },
  ]);

  const apiClient = useApiClient();
  const renameMutation = useRenameDocumentMutation();

  // Add state for confirmation modal
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<null | (() => void)>(null);
  const [confirmMessage, setConfirmMessage] = useState("");
  const [batchLoading, setBatchLoading] = useState(false);

  // Add state to track processing files
  const [processingRAGIds, setProcessingRAGIds] = useState<string[]>([]);

  const getFileIcon = (filename: string) => {
    const ext = filename.split(".").pop()?.toLowerCase();
    switch (ext) {
      case "pdf":
        return <FileText className="w-5 h-5 text-red-500" />;
      case "doc":
      case "docx":
        return <FileText className="w-5 h-5 text-blue-500" />;
      case "png":
      case "jpg":
      case "jpeg":
      case "gif":
        return <ImageIcon className="w-5 h-5 text-green-500" />;
      case "txt":
      case "md":
        return <File className="w-5 h-5 text-gray-500" />;
      default:
        return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return (
      dateObj.toLocaleDateString() +
      " " +
      dateObj.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    if (!currentOrganization) {
      toast.error("Please select an organization first");
      return;
    }

    setUploadingFiles(files);

    // Upload files one by one
    for (const file of files) {
      try {
        await uploadMutation.mutateAsync({
          file,
          organizationId: currentOrganization.id,
        });

        toast.success(`${file.name} uploaded successfully`);
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error(`Failed to upload ${file.name}`);
      }
    }

    setUploadingFiles([]);
    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleAddToRAG = async (documentId: string) => {
    try {
      await addToRAGMutation.mutateAsync(documentId);
      toast.success("Document added to Houston successfully");
    } catch (error) {
      console.error("Error adding to RAG:", error);
      toast.error("Failed to add document to Houston");
    }
  };

  const handleRemoveFromRAG = async (documentId: string) => {
    try {
      await removeFromRAGMutation.mutateAsync(documentId);
      toast.success("Document removed from Houston");
    } catch (error) {
      console.error("Error removing from RAG:", error);
      toast.error("Failed to remove document from Houston");
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await deleteMutation.mutateAsync(documentId);
      toast.success("Document deleted successfully");
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("Failed to delete document");
    }
  };

  const handleConnectCloud = (type: string) => {
    console.log(`Connecting to ${type}`);
    toast.info(`${type} integration coming soon!`);
  };

  const handleDownloadDocument = async (fileId: string) => {
    try {
      const { blob, filename } = await apiClient.downloadDocument(fileId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Failed to download file");
    }
  };

  const openRenameModal = (file: Document) => {
    setRenameFile(file);
    setRenameValue(file.originalName);
    setRenameError("");
    setRenameModalOpen(true);
  };

  const confirmRename = async () => {
    if (!renameFile) return;
    if (!renameValue.trim()) {
      setRenameError("File name cannot be empty");
      return;
    }
    if (renameValue === renameFile.originalName) {
      setRenameModalOpen(false);
      return;
    }
    try {
      await renameMutation.mutateAsync({
        id: renameFile.id,
        originalName: renameValue,
        organizationId: renameFile.organizationId,
      });
      toast.success("File renamed successfully");
      setRenameModalOpen(false);
    } catch (error) {
      setRenameError("Failed to rename file");
    }
  };

  const filteredDocuments = documents.filter((doc) =>
    doc.originalName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Helper to prefetch up to 3 layers deep
  const prefetchFolders = async (path: string, depth: number = 1) => {
    if (depth > 3) return;
    if (oneDriveCache.current[path]) return; // Already cached
    try {
      const items = await apiClient.listOneDriveFiles(path);
      oneDriveCache.current[path] = items;
      // Prefetch subfolders
      await Promise.all(
        items
          .filter((item) => item.folder)
          .map((folder) =>
            prefetchFolders(
              path === "/" ? `/${folder.name}` : `${path}/${folder.name}`,
              depth + 1
            )
          )
      );
    } catch (e) {
      // Ignore errors for prefetch
    }
  };

  // Fetch OneDrive items for a path (with cache)
  const fetchOneDriveItems = async (path: string) => {
    setOneDriveLoading(true);
    setOneDriveError("");
    try {
      // Use cache if available
      if (oneDriveCache.current[path]) {
        setOneDriveItems(oneDriveCache.current[path]);
        setOneDrivePath(path);
        setOneDriveHistory((prev) => {
          if (prev[prev.length - 1] !== path) return [...prev, path];
          return prev;
        });
        setOneDriveLoading(false);
        return;
      }
      // Otherwise, fetch and cache
      const items = await apiClient.listOneDriveFiles(path);
      oneDriveCache.current[path] = items;
      setOneDriveItems(items);
      setOneDrivePath(path);
      setOneDriveHistory((prev) => {
        if (prev[prev.length - 1] !== path) return [...prev, path];
        return prev;
      });
      // Prefetch subfolders (up to 3 layers)
      await prefetchFolders(path);
    } catch (e: any) {
      setOneDriveError(e.message || "Failed to load OneDrive files");
    } finally {
      setOneDriveLoading(false);
    }
  };

  // Sync button: clear cache and re-fetch current folder
  const handleSync = async () => {
    oneDriveCache.current = {};
    await fetchOneDriveItems(oneDrivePath);
  };

  // Update useEffect to use cache-aware fetch
  useEffect(() => {
    (async () => {
      setOneDriveLoading(true);
      try {
        const connected = await checkOneDriveConnection();
        if (connected) {
          // Preload/cache all first-level subfolders and files in root
          const rootItems = oneDriveCache.current["/"] || [];
          await Promise.all(
            rootItems
              .filter((item) => item.folder)
              .map((folder) => prefetchFolders("/" + folder.name, 2)) // depth=2 to get files in subfolder
          );
        }
      } catch (e: any) {
        setOneDriveConnected(false);
        setOneDriveError("Failed to check OneDrive connection");
      } finally {
        setOneDriveLoading(false);
      }
    })();
    // eslint-disable-next-line
  }, []);

  // Handle folder click
  const handleOneDriveFolderClick = (item: OneDriveItem) => {
    const newPath =
      oneDrivePath === "/" ? `/${item.name}` : `${oneDrivePath}/${item.name}`;
    fetchOneDriveItems(newPath);
  };

  // Handle back navigation
  const handleOneDriveBack = () => {
    if (oneDriveHistory.length > 1) {
      const newHistory = [...oneDriveHistory];
      newHistory.pop();
      const prevPath = newHistory[newHistory.length - 1];
      setOneDriveHistory(newHistory);
      fetchOneDriveItems(prevPath);
    }
  };

  // Check OneDrive connection status
  const checkOneDriveConnection = async () => {
    try {
      const connected = await apiClient.isOneDriveConnected();
      setOneDriveConnected(connected);
      if (connected) {
        await fetchOneDriveItems("/");
      }
      return connected;
    } catch (e: any) {
      setOneDriveConnected(false);
      return false;
    }
  };

  // Open OneDrive OAuth in a popup
  const handleConnectOneDrive = () => {
    setOneDriveConnecting(true);
    setOneDriveError("");

    const width = 600;
    const height = 700;
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;
    const popup = window.open(
      apiClient.getOneDriveAuthUrl(),
      "OneDriveAuth",
      `width=${width},height=${height},left=${left},top=${top}`
    );

    // Listen for postMessage from popup
    const messageHandler = async (event: MessageEvent) => {
      if (event.data === "onedrive_connected") {
        // Refresh OneDrive connection state
        await checkOneDriveConnection();

        // Invalidate and refetch documents to show any new processing files
        if (currentOrganization?.id) {
          queryClient.invalidateQueries({
            queryKey: ["documents", currentOrganization.id],
          });
        }

        setOneDriveConnecting(false);
        window.removeEventListener("message", messageHandler);
        if (popup) popup.close();
        toast.success("OneDrive connected successfully!");
      }
    };
    window.addEventListener("message", messageHandler);

    // Declare timer variable for cleanup
    let timer: NodeJS.Timeout;

    // Set up popup onclose handler with debugging
    if (popup) {
      popup.onclose = async () => {
        console.log("Popup onclose event fired!");
        if (timer) clearInterval(timer);
        window.removeEventListener("message", messageHandler);
        // Check connection status when popup closes
        const connected = await checkOneDriveConnection();
        setOneDriveConnecting(false);
        if (connected) {
          // Invalidate and refetch documents to show any new processing files
          if (currentOrganization?.id) {
            queryClient.invalidateQueries({
              queryKey: ["documents", currentOrganization.id],
            });
          }
          toast.success("OneDrive connected successfully!");
        } else {
          toast.error("OneDrive connection was cancelled or failed");
        }
      };

      // Alternative: Use addEventListener for better compatibility
      try {
        popup.addEventListener("beforeunload", () => {
          console.log("Popup beforeunload event fired!");
        });
      } catch (e) {
        // Fallback for older browsers
        popup.onbeforeunload = () => {
          console.log("Popup onbeforeunload event fired!");
        };
      }
    }

    // Fallback: Poll for popup close (most reliable method)
    timer = setInterval(async () => {
      console.log("Checking popup status...", popup?.closed);
      if (popup && popup.closed) {
        console.log("Popup detected as closed via polling!");
        clearInterval(timer);
        window.removeEventListener("message", messageHandler);
        // Check connection status when popup closes
        const connected = await checkOneDriveConnection();
        setOneDriveConnecting(false);
        if (connected) {
          // Invalidate and refetch documents to show any new processing files
          if (currentOrganization?.id) {
            queryClient.invalidateQueries({
              queryKey: ["documents", currentOrganization.id],
            });
          }
          toast.success("OneDrive connected successfully!");
        } else {
          toast.error("OneDrive connection was cancelled or failed");
        }
      }
    }, 500);

    // Timeout after 5 minutes
    setTimeout(() => {
      if (timer) clearInterval(timer);
      window.removeEventListener("message", messageHandler);
      if (popup && !popup.closed) {
        popup.close();
        setOneDriveConnecting(false);
        toast.error("OneDrive connection timed out");
      }
    }, 5 * 60 * 1000);
  };

  // Helper: Breadcrumbs for OneDrive path
  const renderBreadcrumbs = () => {
    const parts = oneDrivePath.split("/").filter(Boolean);
    let path = "";
    return (
      <Breadcrumb className="flex items-center gap-x-2">
        <BreadcrumbItem key="root">
          <BreadcrumbLink
            onClick={() => fetchOneDriveItems("/")}
            className="cursor-pointer"
          >
            Root
          </BreadcrumbLink>
        </BreadcrumbItem>
        {parts.map((part, idx) => {
          path += `/${part}`;
          return [
            <BreadcrumbSeparator key={`sep-${idx}`} className="flex" />,
            <BreadcrumbItem key={path}>
              <BreadcrumbLink
                onClick={() => fetchOneDriveItems(path)}
                className="cursor-pointer"
              >
                {part}
              </BreadcrumbLink>
            </BreadcrumbItem>,
          ];
        })}
      </Breadcrumb>
    );
  };

  // Helper: Get RAG status for a OneDrive file (by name and path)
  const isFileInRAG = (item: OneDriveItem, currentPath: string) => {
    // Try to match by both name and path (if available in your Document type)
    // For now, fallback to name only (improve if you store path info in Document)
    return documents.some((doc) => doc.originalName === item.name);
  };

  // Update RAG folder badge to use cached data only
  const countRAGInFolder = async (folder: OneDriveItem) => {
    const folderPath =
      oneDrivePath === "/"
        ? `/${folder.name}`
        : `${oneDrivePath}/${folder.name}`;
    const items = oneDriveCache.current[folderPath] || [];
    const files = items.filter((item) => item.file);
    let inRAG = 0;
    files.forEach((file) => {
      if (isFileInRAG(file, folderPath)) inRAG++;
    });
    return { inRAG, total: files.length };
  };

  // Update handleAddOneDriveFileToRAG to track processing
  const handleAddOneDriveFileToRAG = async (item: OneDriveItem) => {
    if (!currentOrganization) {
      toast.error("Please select an organization first");
      return;
    }
    try {
      setProcessingRAGIds((ids) => [...ids, item.id]);
      setBatchLoading(true);
      const result = await apiClient.addOneDriveFileToRAG({
        fileId: item.id,
        fileName: item.name,
        organizationId: currentOrganization.id,
      });

      // Check if the file is being processed in the background
      if (result.data.status === "PROCESSING") {
        toast.success("File is being processed in the background!");
      } else {
        toast.success("File added to Houston!");
      }

      // Invalidate and refetch documents to show the new/processing document
      queryClient.invalidateQueries({
        queryKey: ["documents", currentOrganization.id],
      });

      fetchOneDriveItems(oneDrivePath); // Refresh UI
    } catch (e) {
      toast.error("Failed to add file to Houston");
    } finally {
      setBatchLoading(false);
      setProcessingRAGIds((ids) => ids.filter((id) => id !== item.id));
    }
  };

  // Update handleBatchRAG for folders to track processing
  const handleBatchRAG = useCallback(
    async (folder: OneDriveItem, action: "add" | "remove") => {
      if (!currentOrganization) {
        toast.error("Please select an organization first");
        return;
      }
      setBatchLoading(true);
      const folderPath =
        oneDrivePath === "/"
          ? `/${folder.name}`
          : `${oneDrivePath}/${folder.name}`;
      const items = oneDriveCache.current[folderPath] || [];
      const files = items.filter((item) => item.file);
      try {
        if (action === "add") {
          setProcessingRAGIds((ids) => [...ids, ...files.map((f) => f.id)]);
          for (const file of files) {
            await apiClient.addOneDriveFileToRAG({
              fileId: file.id,
              fileName: file.name,
              organizationId: currentOrganization.id,
            });
          }
        } else {
          setProcessingRAGIds((ids) => [...ids, ...files.map((f) => f.id)]);
          for (const file of files) {
            await removeFromRAGMutation.mutateAsync(file.id);
          }
        }
        toast.success(
          `${action === "add" ? "Added" : "Removed"} all files in '${
            folder.name
          }' ${action === "add" ? "to" : "from"} Houston.`
        );
        fetchOneDriveItems(oneDrivePath);
      } catch (e) {
        toast.error("Batch operation failed");
      } finally {
        setBatchLoading(false);
        setProcessingRAGIds((ids) =>
          ids.filter((id) => !files.map((f) => f.id).includes(id))
        );
        setConfirmModalOpen(false);
      }
    },
    [
      apiClient,
      removeFromRAGMutation,
      oneDriveCache,
      oneDrivePath,
      currentOrganization,
    ]
  );

  // Remove from Houston for OneDrive files
  const handleRemoveOneDriveFileFromRAG = async (item: OneDriveItem) => {
    setProcessingRAGIds((ids) => [...ids, item.id]);
    try {
      await removeFromRAGMutation.mutateAsync(item.id);
      toast.success("File removed from Houston!");
      fetchOneDriveItems(oneDrivePath);
    } catch (e) {
      toast.error("Failed to remove file from Houston");
    } finally {
      setProcessingRAGIds((ids) => ids.filter((id) => id !== item.id));
    }
  };

  // Update RAG folder badge to use cached data only
  const AsyncRAGFolderBadge = ({ folder }: { folder: OneDriveItem }) => {
    const [status, setStatus] = useState<{
      inRAG: number;
      total: number;
    } | null>(null);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
      setLoading(true);
      countRAGInFolder(folder).then((res: { inRAG: number; total: number }) => {
        setStatus(res);
        setLoading(false);
      });
    }, [folder]);
    if (loading) return <Badge variant="outline">Checking...</Badge>;
    if (!status) return null;
    return (
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="border-slate-200 text-slate-600">
          {status.inRAG} / {status.total} in RAG
        </Badge>
        {status.total === 0 ? null : status.inRAG < status.total ? (
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setConfirmMessage(
                `Add all files in '${folder.name}' to Houston?`
              );
              setConfirmAction(() => () => handleBatchRAG(folder, "add"));
              setConfirmModalOpen(true);
            }}
          >
            Add All to Houston
          </Button>
        ) : status.inRAG === status.total ? (
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setConfirmMessage(
                `Remove all files in '${folder.name}' from Houston?`
              );
              setConfirmAction(() => () => handleBatchRAG(folder, "remove"));
              setConfirmModalOpen(true);
            }}
          >
            Remove All
          </Button>
        ) : null}
      </div>
    );
  };

  // In the Houston Document Processing tab, merge processingRAGIds with documents to show files being processed
  const processingFiles = oneDriveItems.filter((item) =>
    processingRAGIds.includes(item.id)
  );
  const houstonDocuments = [
    ...documents.filter((f) => f.inRAG),
    ...processingFiles.map((item) => ({
      id: item.id,
      originalName: item.name,
      createdAt: item.lastModifiedDateTime,
      status: "PROCESSING",
      inRAG: false,
      name: item.name,
      size: item.size || 0,
      lastModifiedDateTime: item.lastModifiedDateTime,
      webUrl: item.webUrl || "",
    })),
  ];

  // Type guard to distinguish Document from OneDriveItem/processing object
  function isDocument(obj: any): obj is Document {
    return (
      obj &&
      typeof obj.originalName === "string" &&
      "createdAt" in obj &&
      !("name" in obj && "lastModifiedDateTime" in obj)
    );
  }

  return (
    <div className="flex flex-col h-full bg-slate-50 dark:bg-slate-900">
      {/* Header */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-slate-900 dark:text-white">
              Knowledge Base
            </h1>
            <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400">
              Manage your files, integrations, and document processing
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge
              variant="outline"
              className="border-indigo-200 text-indigo-700 bg-indigo-50 text-xs sm:text-sm"
            >
              {documents.filter((f) => f.inRAG).length} files in Houston
            </Badge>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search files and integrations..."
            className="pl-10 border-slate-200 focus:border-indigo-300"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-3 sm:p-6">
        <Tabs
          value={currentTab}
          onValueChange={handleTabChange}
          className="h-full"
        >
          <TabsList className="grid w-full grid-cols-4 mb-4 sm:mb-6">
            <TabsTrigger value="files" className="text-xs sm:text-sm">
              Files
            </TabsTrigger>
            <TabsTrigger value="cloud" className="text-xs sm:text-sm">
              Data Integrations
            </TabsTrigger>
            <TabsTrigger value="websites" className="text-xs sm:text-sm">
              Websites
            </TabsTrigger>
            <TabsTrigger value="rag" className="text-xs sm:text-sm">
              Houston Document Processing
            </TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="mt-0 h-full">
            <div className="flex flex-col h-full">
              {/* Toolbar */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                    onClick={handleFileUpload}
                    disabled={uploadMutation.isPending}
                  >
                    {uploadMutation.isPending ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Upload className="w-4 h-4 mr-2" />
                    )}
                    <span className="hidden sm:inline">Upload Files</span>
                    <span className="sm:hidden">Upload</span>
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    className="hidden"
                    accept=".pdf,.docx,.doc,.txt,.md,.csv,.xlsx,.xls,.pptx,.ppt"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant={viewMode === "list" ? "default" : "outline"}
                    onClick={() => setViewMode("list")}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={viewMode === "grid" ? "default" : "outline"}
                    onClick={() => setViewMode("grid")}
                    className="hidden sm:flex"
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Upload Progress */}
              {uploadingFiles.length > 0 && (
                <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Uploading Files...
                  </h4>
                  {uploadingFiles.map((file) => (
                    <div
                      key={file.name}
                      className="flex items-center space-x-2 text-sm text-blue-700 dark:text-blue-300"
                    >
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>{file.name}</span>
                    </div>
                  ))}
                </div>
              )}

              {/* File List */}
              <div className="flex-1 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex flex-col">
                {/* Desktop Header */}
                <div className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-200 dark:border-slate-700 text-sm font-medium text-slate-500 dark:text-slate-400">
                  <div className="col-span-5">Name</div>
                  <div className="col-span-2">Size</div>
                  <div className="col-span-2">Modified</div>
                  <div className="col-span-2">RAG Status</div>
                  <div className="col-span-1"></div>
                </div>

                <ScrollArea className="flex-1 min-h-0">
                  <div className="min-h-full">
                    {documentsLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
                        <span className="ml-2 text-slate-500">
                          Loading documents...
                        </span>
                      </div>
                    ) : filteredDocuments.length === 0 ? (
                      <div className="text-center py-16">
                        <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                          No documents yet
                        </h3>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                          Upload your first document to get started
                        </p>
                        <Button
                          onClick={handleFileUpload}
                          className="bg-indigo-600 hover:bg-indigo-700"
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Upload Document
                        </Button>
                      </div>
                    ) : (
                      filteredDocuments.map((file) => (
                        <div key={file.id}>
                          {/* Mobile Layout */}
                          <div className="sm:hidden p-4 border-b border-slate-100 dark:border-slate-700">
                            <div className="flex items-start space-x-3">
                              <div className="mt-1">
                                {getFileIcon(file.originalName)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-slate-900 dark:text-white truncate">
                                  {file.originalName}
                                </h4>
                                <div className="flex items-center space-x-4 mt-1 text-xs text-slate-500 dark:text-slate-400">
                                  <span>{formatFileSize(file.size)}</span>
                                  <span>{formatDate(file.createdAt)}</span>
                                </div>
                                <div className="flex items-center justify-between mt-2">
                                  <div className="flex items-center space-x-2">
                                    {file.status === "PROCESSING" ? (
                                      <Badge
                                        variant="outline"
                                        className="border-yellow-200 text-yellow-700 bg-yellow-50 text-xs"
                                      >
                                        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                                        Processing
                                      </Badge>
                                    ) : file.status === "FAILED" ? (
                                      <Badge
                                        variant="outline"
                                        className="border-red-200 text-red-700 bg-red-50 text-xs"
                                      >
                                        <AlertCircle className="w-3 h-3 mr-1" />
                                        Failed
                                      </Badge>
                                    ) : file.inRAG ? (
                                      <Button
                                        size="sm"
                                        variant="default"
                                        onClick={() =>
                                          isDocument(file)
                                            ? handleRemoveFromRAG(file.id)
                                            : handleRemoveOneDriveFileFromRAG(
                                                file
                                              )
                                        }
                                        className="bg-green-600 hover:bg-green-700 text-white text-xs"
                                      >
                                        <CheckCircle className="w-3 h-3 mr-1" />
                                        In Houston
                                      </Button>
                                    ) : (
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() =>
                                          isDocument(file)
                                            ? handleAddToRAG(file.id)
                                            : handleAddOneDriveFileToRAG(file)
                                        }
                                        className="border-slate-300 hover:bg-slate-50 text-xs"
                                      >
                                        Add to Houston
                                      </Button>
                                    )}
                                  </div>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 w-8 p-0"
                                      >
                                        <MoreVertical className="w-4 h-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleDownloadDocument(file.id)
                                        }
                                      >
                                        <Download className="w-4 h-4 mr-2" />
                                        Download
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() => openRenameModal(file)}
                                      >
                                        <Edit className="w-4 h-4 mr-2" />
                                        Rename
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        className="text-red-600"
                                        onClick={() =>
                                          handleDeleteDocument(file.id)
                                        }
                                      >
                                        <Trash2 className="w-4 h-4 mr-2" />
                                        Delete
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Desktop Layout */}
                          <div className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer">
                            <div className="col-span-5 flex items-center space-x-3">
                              {getFileIcon(file.originalName)}
                              <span className="text-sm text-slate-900 dark:text-white truncate">
                                {file.originalName}
                              </span>
                            </div>
                            <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
                              {formatFileSize(file.size)}
                            </div>
                            <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
                              {formatDate(file.createdAt)}
                            </div>
                            <div className="col-span-2">
                              {file.status === "PROCESSING" ? (
                                <Badge
                                  variant="outline"
                                  className="border-yellow-200 text-yellow-700 bg-yellow-50"
                                >
                                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                                  Processing
                                </Badge>
                              ) : file.status === "FAILED" ? (
                                <Badge
                                  variant="outline"
                                  className="border-red-200 text-red-700 bg-red-50"
                                >
                                  <AlertCircle className="w-3 h-3 mr-1" />
                                  Failed
                                </Badge>
                              ) : file.inRAG ? (
                                <Button
                                  size="sm"
                                  variant="default"
                                  onClick={() =>
                                    isDocument(file)
                                      ? handleRemoveFromRAG(file.id)
                                      : handleRemoveOneDriveFileFromRAG(file)
                                  }
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                >
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  In Houston
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    isDocument(file)
                                      ? handleAddToRAG(file.id)
                                      : handleAddOneDriveFileToRAG(file)
                                  }
                                  className="border-slate-300 hover:bg-slate-50 hover:text-slate-900"
                                >
                                  Add to Houston
                                </Button>
                              )}
                            </div>
                            <div className="col-span-1 flex justify-end">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreVertical className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleDownloadDocument(file.id)
                                    }
                                  >
                                    <Download className="w-4 h-4 mr-2" />
                                    Download
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openRenameModal(file)}
                                  >
                                    <Edit className="w-4 h-4 mr-2" />
                                    Rename
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-red-600"
                                    onClick={() =>
                                      handleDeleteDocument(file.id)
                                    }
                                  >
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="cloud" className="mt-0">
            <div className="grid gap-4 sm:gap-6">
              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Cloud className="w-5 h-5" />
                    <span>OneDrive</span>
                  </CardTitle>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Connect your OneDrive account to browse and import files.
                  </p>
                </CardHeader>
                <CardContent>
                  {oneDriveLoading ? (
                    <div className="flex items-center space-x-2 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading OneDrive...</span>
                    </div>
                  ) : oneDriveConnecting ? (
                    <div className="flex items-center space-x-2 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Connecting to OneDrive...</span>
                    </div>
                  ) : oneDriveConnected === false ? (
                    <Button
                      onClick={handleConnectOneDrive}
                      disabled={oneDriveConnecting}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50"
                    >
                      <Cloud className="w-4 h-4 mr-2" /> Connect OneDrive
                    </Button>
                  ) : oneDriveError ? (
                    <div className="text-red-600">{oneDriveError}</div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <span className="text-green-600 font-medium">
                            OneDrive Connected
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            try {
                              await apiClient.disconnectOneDrive();
                              setOneDriveConnected(false);
                              setOneDriveItems([]);
                              setOneDrivePath("/");
                              setOneDriveHistory(["/"]);
                              oneDriveCache.current = {};
                              toast.success("OneDrive disconnected");
                            } catch (error) {
                              toast.error("Failed to disconnect OneDrive");
                            }
                          }}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          Disconnect
                        </Button>
                      </div>
                      <div className="flex items-center mb-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleOneDriveBack}
                          disabled={oneDriveHistory.length <= 1}
                        >
                          Back
                        </Button>
                        <span className="ml-4 text-slate-500 flex-1">
                          {renderBreadcrumbs()}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="ml-auto"
                          onClick={handleSync}
                        >
                          Sync
                        </Button>
                      </div>
                      {/* Unified Files/Folders List UI */}
                      <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex flex-col">
                        {/* Desktop Header */}
                        <div className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-200 dark:border-slate-700 text-sm font-medium text-slate-500 dark:text-slate-400">
                          <div className="col-span-5">Name</div>
                          <div className="col-span-2">Size</div>
                          <div className="col-span-2">Modified</div>
                          <div className="col-span-2">RAG Status</div>
                          <div className="col-span-1"></div>
                        </div>
                        <ScrollArea className="flex-1 min-h-0">
                          <div className="min-h-full">
                            {oneDriveItems.length === 0 ? (
                              <div className="text-center py-16">
                                <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                                  No files or folders
                                </h3>
                              </div>
                            ) : (
                              oneDriveItems.map((item) => (
                                <div key={item.id}>
                                  {/* Mobile Layout */}
                                  <div className="sm:hidden p-4 border-b border-slate-100 dark:border-slate-700">
                                    <div className="flex items-start space-x-3">
                                      <div className="mt-1">
                                        {item.folder ? (
                                          <Folder className="w-5 h-5 text-blue-500" />
                                        ) : (
                                          getFileIcon(item.name)
                                        )}
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <h4
                                          className={
                                            item.folder
                                              ? "font-medium text-blue-700 dark:text-blue-300 truncate cursor-pointer hover:underline"
                                              : "font-medium text-slate-900 dark:text-white truncate"
                                          }
                                          onClick={() =>
                                            item.folder &&
                                            handleOneDriveFolderClick(item)
                                          }
                                        >
                                          {item.name}
                                        </h4>
                                        <div className="flex items-center space-x-4 mt-1 text-xs text-slate-500 dark:text-slate-400">
                                          <span>
                                            {item.folder
                                              ? "--"
                                              : formatFileSize(item.size)}
                                          </span>
                                          <span>
                                            {formatDate(
                                              item.lastModifiedDateTime
                                            )}
                                          </span>
                                        </div>
                                        <div className="flex items-center justify-between mt-2">
                                          <div className="flex items-center space-x-2">
                                            {item.folder ? (
                                              <Button
                                                size="sm"
                                                variant="outline"
                                                disabled={batchLoading}
                                                onClick={() => {
                                                  setConfirmMessage(
                                                    `Add all files in '${item.name}' to Houston?`
                                                  );
                                                  setConfirmAction(
                                                    () => () =>
                                                      handleBatchRAG(
                                                        item,
                                                        "add"
                                                      )
                                                  );
                                                  setConfirmModalOpen(true);
                                                }}
                                                className="border-slate-300 hover:bg-slate-50 text-xs"
                                              >
                                                Add All to RAG
                                              </Button>
                                            ) : (
                                              <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() =>
                                                  handleAddOneDriveFileToRAG(
                                                    item
                                                  )
                                                }
                                                className="border-slate-300 hover:bg-slate-50 text-xs"
                                              >
                                                Add to Houston
                                              </Button>
                                            )}
                                          </div>
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0"
                                              >
                                                <MoreVertical className="w-4 h-4" />
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                              {!item.folder && (
                                                <DropdownMenuItem
                                                  onClick={() =>
                                                    window.open(
                                                      item.webUrl,
                                                      "_blank"
                                                    )
                                                  }
                                                >
                                                  <Download className="w-4 h-4 mr-2" />
                                                  Download
                                                </DropdownMenuItem>
                                              )}
                                              {item.folder ? (
                                                <DropdownMenuItem
                                                  onClick={() => {
                                                    setConfirmMessage(
                                                      `Remove all files in '${item.name}' from Houston?`
                                                    );
                                                    setConfirmAction(
                                                      () => () =>
                                                        handleBatchRAG(
                                                          item,
                                                          "remove"
                                                        )
                                                    );
                                                    setConfirmModalOpen(true);
                                                  }}
                                                >
                                                  <Trash2 className="w-4 h-4 mr-2" />
                                                  Remove All
                                                </DropdownMenuItem>
                                              ) : (
                                                <DropdownMenuItem
                                                  onClick={() =>
                                                    handleRemoveOneDriveFileFromRAG(
                                                      item
                                                    )
                                                  }
                                                >
                                                  <Trash2 className="w-4 h-4 mr-2" />
                                                  Remove from Houston
                                                </DropdownMenuItem>
                                              )}
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  {/* Desktop Layout */}
                                  <div className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer">
                                    <div className="col-span-5 flex items-center space-x-3">
                                      {item.folder ? (
                                        <Folder className="w-5 h-5 text-blue-500" />
                                      ) : (
                                        getFileIcon(item.name)
                                      )}
                                      <span
                                        className={
                                          item.folder
                                            ? "text-blue-700 dark:text-blue-300 truncate hover:underline"
                                            : "text-sm text-slate-900 dark:text-white truncate"
                                        }
                                        onClick={() =>
                                          item.folder &&
                                          handleOneDriveFolderClick(item)
                                        }
                                        style={{
                                          cursor: item.folder
                                            ? "pointer"
                                            : "default",
                                        }}
                                      >
                                        {item.name}
                                      </span>
                                    </div>
                                    <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
                                      {item.folder
                                        ? "--"
                                        : formatFileSize(item.size)}
                                    </div>
                                    <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
                                      {formatDate(item.lastModifiedDateTime)}
                                    </div>
                                    <div className="col-span-2">
                                      {item.folder ? (
                                        <AsyncRAGFolderBadge folder={item} />
                                      ) : isFileInRAG(item, oneDrivePath) ? (
                                        <Badge
                                          variant="default"
                                          className="bg-green-600 text-white"
                                        >
                                          <CheckCircle className="w-3 h-3 mr-1" />{" "}
                                          In Houston
                                        </Badge>
                                      ) : (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() =>
                                            handleAddOneDriveFileToRAG(item)
                                          }
                                        >
                                          Add to Houston
                                        </Button>
                                      )}
                                    </div>
                                    <div className="col-span-1 flex justify-end">
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0"
                                          >
                                            <MoreVertical className="w-4 h-4" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          {!item.folder && (
                                            <DropdownMenuItem
                                              onClick={() =>
                                                window.open(
                                                  item.webUrl,
                                                  "_blank"
                                                )
                                              }
                                            >
                                              <Download className="w-4 h-4 mr-2" />
                                              Download
                                            </DropdownMenuItem>
                                          )}
                                          {item.folder ? (
                                            <DropdownMenuItem
                                              onClick={() => {
                                                setConfirmMessage(
                                                  `Remove all files in '${item.name}' from Houston?`
                                                );
                                                setConfirmAction(
                                                  () => () =>
                                                    handleBatchRAG(
                                                      item,
                                                      "remove"
                                                    )
                                                );
                                                setConfirmModalOpen(true);
                                              }}
                                            >
                                              <Trash2 className="w-4 h-4 mr-2" />
                                              Remove All
                                            </DropdownMenuItem>
                                          ) : (
                                            <DropdownMenuItem
                                              onClick={() =>
                                                handleRemoveOneDriveFileFromRAG(
                                                  item
                                                )
                                              }
                                            >
                                              <Trash2 className="w-4 h-4 mr-2" />
                                              Remove from Houston
                                            </DropdownMenuItem>
                                          )}
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </div>
                                  </div>
                                </div>
                              ))
                            )}
                          </div>
                        </ScrollArea>
                      </div>
                      {/* Confirmation Modal for batch actions */}
                      <ConfirmDialog
                        open={confirmModalOpen}
                        onOpenChange={setConfirmModalOpen}
                      >
                        <ConfirmDialogContent>
                          <ConfirmDialogHeader>
                            <ConfirmDialogTitle>
                              Confirm Action
                            </ConfirmDialogTitle>
                            <ConfirmDialogDescription>
                              {confirmMessage}
                            </ConfirmDialogDescription>
                          </ConfirmDialogHeader>
                          <ConfirmDialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => setConfirmModalOpen(false)}
                              disabled={batchLoading}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={confirmAction || (() => {})}
                              disabled={batchLoading}
                            >
                              {batchLoading ? "Processing..." : "Confirm"}
                            </Button>
                          </ConfirmDialogFooter>
                        </ConfirmDialogContent>
                      </ConfirmDialog>
                    </div>
                  )}
                </CardContent>
              </Card>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {cloudIntegrations.map((integration) => (
                  <Card
                    key={integration.id}
                    className="border-slate-200 dark:border-slate-700"
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center">
                            <Cloud className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">
                              {integration.name}
                            </CardTitle>
                            <p className="text-sm text-slate-500 dark:text-slate-400">
                              {integration.connected
                                ? "Connected"
                                : "Not connected"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {integration.connected ? (
                            <Badge
                              variant="outline"
                              className="border-green-200 text-green-700 bg-green-50"
                            >
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Connected
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="border-slate-200 text-slate-600"
                            >
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Disconnected
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {integration.connected ? (
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-600 dark:text-slate-400">
                              Files available:
                            </span>
                            <span className="font-medium">
                              {integration.filesCount?.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-600 dark:text-slate-400">
                              Last sync:
                            </span>
                            <span className="font-medium">
                              {integration.lastSync
                                ? formatDate(integration.lastSync)
                                : "Never"}
                            </span>
                          </div>
                          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                            >
                              Sync Now
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                            >
                              Browse Files
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            Connect your {integration.name} account to access
                            and manage your files directly from Talknician.
                          </p>
                          <Button
                            onClick={() => handleConnectCloud(integration.type)}
                            className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                          >
                            Connect {integration.name}
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="rag" className="mt-0">
            <div className="space-y-4 sm:space-y-6">
              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <HardDrive className="w-5 h-5" />
                    <span>Houston Document Processing</span>
                  </CardTitle>
                  <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400">
                    Documents added to the make available for Houston to
                    reference in conversations.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <div className="text-2xl font-bold text-slate-900 dark:text-white">
                        {documents.filter((f) => f.inRAG).length}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        Documents in RAG
                      </div>
                    </div>
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <div className="text-2xl font-bold text-slate-900 dark:text-white">
                        {(
                          documents.reduce((acc, f) => acc + f.size, 0) /
                          (1024 * 1024)
                        ).toFixed(1)}
                        MB
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        Total size processed
                      </div>
                    </div>
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        Active
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        Processing status
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-slate-900 dark:text-white">
                      Documents in Houston System
                    </h4>
                    {houstonDocuments.map((file) => (
                      <div
                        key={file.id}
                        className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg space-y-2 sm:space-y-0"
                      >
                        <div className="flex items-center space-x-3">
                          {getFileIcon(file.originalName)}
                          <div>
                            <div className="font-medium text-slate-900 dark:text-white">
                              {file.originalName}
                            </div>
                            <div className="text-sm text-slate-500 dark:text-slate-400">
                              Added {formatDate(file.createdAt)}
                            </div>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            isDocument(file)
                              ? handleRemoveFromRAG(file.id)
                              : handleRemoveOneDriveFileFromRAG(file)
                          }
                          className="border-red-200 text-red-600 hover:bg-red-50 w-full sm:w-auto"
                        >
                          Remove from RAG
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="websites" className="mt-0">
            <div className="space-y-4 sm:space-y-6">
              <Card className="border-slate-200 dark:border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Globe className="w-5 h-5" />
                    <span>Website RAG Integration</span>
                  </CardTitle>
                  <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400">
                    Add websites to your knowledge base for Houston to reference
                    in conversations.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Add Website Form */}
                    <div className="flex space-x-2">
                      <Input
                        placeholder="Enter website URL (e.g., https://example.com)"
                        className="flex-1"
                      />
                      <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Website
                      </Button>
                    </div>

                    {/* Connected Websites List */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-slate-900 dark:text-white">
                        Connected Websites
                      </h4>

                      {/* Placeholder for when no websites are connected */}
                      <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                        <Globe className="w-12 h-12 mx-auto mb-3 opacity-50" />
                        <p className="text-sm">No websites connected yet</p>
                        <p className="text-xs mt-1">
                          Add a website URL above to get started
                        </p>
                      </div>

                      {/* Example of how connected websites would look */}
                      {/*
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <Globe className="w-5 h-5 text-slate-600" />
                            <div>
                              <div className="font-medium text-slate-900 dark:text-white">
                                Example Website
                              </div>
                              <div className="text-sm text-slate-500 dark:text-slate-400">
                                https://example.com
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-green-600 border-green-200">
                              Active
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <ExternalLink className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      */}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <Dialog open={renameModalOpen} onOpenChange={setRenameModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename File</DialogTitle>
            <DialogDescription>
              Enter a new name for your file.
            </DialogDescription>
          </DialogHeader>
          <Input
            value={renameValue}
            onChange={(e) => setRenameValue(e.target.value)}
            autoFocus
            disabled={renameMutation.isPending}
            onKeyDown={(e) => {
              if (e.key === "Enter") confirmRename();
            }}
          />
          {renameError && (
            <div className="text-red-600 text-sm mt-1">{renameError}</div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRenameModalOpen(false)}
              disabled={renameMutation.isPending}
            >
              Cancel
            </Button>
            <Button onClick={confirmRename} disabled={renameMutation.isPending}>
              {renameMutation.isPending ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
