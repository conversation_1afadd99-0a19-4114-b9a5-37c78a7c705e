"use client";

import React, { useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import ReactMarkdown from "react-markdown";
import {
  Send,
  Plus,
  MessageSquare,
  Search,
  Calendar,
  MoreVertical,
  Trash2,
  Bot,
  User,
  Loader2,
  AlertCircle,
  Settings,
  Globe,
  FileText,
  ExternalLink,
  Sparkles,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useHoustonChat } from "@/hooks/useHoustonChat";
import useHoustonChatSSE from "@/hooks/useHoustonChatSSE";

// Source Reference Component
const SourceReference = ({ source }: { source: any }) => {
  const getSourceIcon = () => {
    switch (source.type) {
      case "document":
        return <FileText className="h-3 w-3" />;
      case "web":
        return <Globe className="h-3 w-3" />;
      default:
        return <Sparkles className="h-3 w-3" />;
    }
  };

  const handleSourceClick = () => {
    if (source.url) {
      window.open(source.url, "_blank");
    }
  };

  return (
    <Badge
      variant="outline"
      className="text-xs cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={handleSourceClick}
    >
      {getSourceIcon()}
      <span className="ml-1">{source.title}</span>
      {source.url && <ExternalLink className="h-2 w-2 ml-1" />}
    </Badge>
  );
};

// Streaming Message Component
const StreamingMessage = ({
  content,
  isStreaming,
  sources,
}: {
  content: string;
  isStreaming?: boolean;
  sources?: any[];
}) => {
  return (
    <div className="flex items-start">
      <div className="flex-1">
        <ReactMarkdown>{content}</ReactMarkdown>
        {isStreaming && content && (
          <span className="inline-block w-0.5 h-4 bg-indigo-500 ml-1 animate-pulse"></span>
        )}
        {sources && sources.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            <span className="text-xs text-gray-500 mr-2">Sources:</span>
            {sources.map((source, index) => (
              <SourceReference key={index} source={source} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  createdAt: string;
  isStreaming?: boolean;
  sources?: SourceReference[];
}

interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

interface SourceReference {
  type: "document" | "web" | "general";
  title: string;
  content?: string;
  url?: string;
  documentId?: string;
}

interface ChatSettings {
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
}

export default function HoustonPage() {
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();

  const {
    conversations,
    filteredConversations,
    currentConversation,
    inputValue,
    setInputValue,
    isLoading,
    isStreaming,
    isMobileConversationsOpen,
    setIsMobileConversationsOpen,
    conversationSearchQuery,
    setConversationSearchQuery,
    error,
    setError,
    chatSettings,
    setChatSettings,
    sources,
    setSources,
    currentRequestWebSearch,
    setCurrentRequestWebSearch,
    loadConversations,
    loadConversation,
    createNewConversation,
    deleteConversation,
    loadChatSettings,
    updateChatSettings,
  } = useHoustonChat();

  const { messages, streamingState, sendMessage } = useHoustonChatSSE();

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load conversations on mount
  useEffect(() => {
    if (currentOrganization && accessToken) {
      loadConversations();
    }
  }, [currentOrganization, accessToken]);

  // Clean up EventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!inputValue.trim() || !currentConversation || isLoading) return;

      sendMessage({
        conversationId: currentConversation?.id || "",
        message: inputValue,
        settings: chatSettings,
      });
    }
  };
  const handleSendMessage = () => {
    if (!inputValue.trim() || !currentConversation || isLoading) return;

    sendMessage({
      conversationId: currentConversation?.id || "",
      message: inputValue,
      settings: chatSettings,
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  // Settings Dialog Component
  const SettingsDialog = () => {
    const [localSettings, setLocalSettings] =
      React.useState<ChatSettings>(chatSettings);
    const [isOpen, setIsOpen] = React.useState(false);

    const handleSave = async () => {
      await updateChatSettings(localSettings);
      setIsOpen(false);
    };

    const personalityPresets = [
      {
        name: "Professional",
        prompt:
          "You are a professional AI assistant. Be formal, precise, and helpful.",
      },
      {
        name: "Friendly",
        prompt:
          "You are a friendly AI assistant. Be warm, conversational, and approachable.",
      },
      {
        name: "Technical",
        prompt:
          "You are a technical AI assistant. Focus on detailed, accurate technical information.",
      },
      {
        name: "Creative",
        prompt:
          "You are a creative AI assistant. Be imaginative, inspiring, and think outside the box.",
      },
    ];

    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Chat Settings</DialogTitle>
            <DialogDescription>
              Customize your AI chat experience with Houston.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            {/* Web Search Toggle */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Web Search</Label>
                <div className="text-sm text-gray-500">
                  Enable web search for current information
                </div>
              </div>
              <Switch
                checked={localSettings.enableWebSearch || false}
                onCheckedChange={(checked) =>
                  setLocalSettings({
                    ...localSettings,
                    enableWebSearch: checked,
                  })
                }
              />
            </div>

            <Separator />

            {/* Personality Presets */}
            <div className="space-y-3">
              <Label className="text-base">AI Personality</Label>
              <div className="grid grid-cols-2 gap-2">
                {personalityPresets.map((preset) => (
                  <Button
                    key={preset.name}
                    variant={
                      localSettings.personality === preset.prompt
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setLocalSettings({
                        ...localSettings,
                        personality: preset.prompt,
                      })
                    }
                  >
                    {preset.name}
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Custom System Prompt */}
            <div className="space-y-3">
              <Label className="text-base">Custom System Prompt</Label>
              <Textarea
                placeholder="Enter a custom system prompt to define Houston's behavior..."
                value={localSettings.systemPrompt || ""}
                onChange={(e) =>
                  setLocalSettings({
                    ...localSettings,
                    systemPrompt: e.target.value,
                  })
                }
                rows={4}
              />
              <div className="text-xs text-gray-500">
                This will override the personality preset if provided.
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Settings</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  const ConversationsSidebar = () => (
    <div className="w-full h-full bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
            Houston
          </h2>
          <div className="flex items-center space-x-2">
            <SettingsDialog />
            <Button
              size="sm"
              variant="outline"
              onClick={createNewConversation}
              className="border-indigo-200 dark:border-indigo-800 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-950"
            >
              <Plus className="w-4 h-4 mr-1" />
              New Chat
            </Button>
          </div>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            placeholder="Search conversations..."
            value={conversationSearchQuery}
            onChange={(e) => setConversationSearchQuery(e.target.value)}
            className="pl-10 border-slate-200 dark:border-slate-700 focus:border-indigo-300"
          />
        </div>
      </div>

      {/* Conversations */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {isLoading && conversations.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="text-center py-8 text-slate-500 dark:text-slate-400">
              {conversationSearchQuery
                ? "No conversations found"
                : "No conversations yet"}
            </div>
          ) : (
            filteredConversations.map((conversation) => (
              <Card
                key={conversation.id}
                className={`mb-2 cursor-pointer transition-colors border ${
                  currentConversation?.id === conversation.id
                    ? "bg-indigo-50 dark:bg-indigo-950 border-indigo-200 dark:border-indigo-800"
                    : "hover:bg-slate-50 dark:hover:bg-slate-700 border-transparent hover:border-slate-200 dark:hover:border-slate-600"
                }`}
                onClick={() => {
                  loadConversation(conversation.id);
                  setIsMobileConversationsOpen(false);
                }}
              >
                <CardContent className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-slate-900 dark:text-white truncate text-sm">
                        {conversation.title}
                      </h4>
                      <div className="flex items-center mt-2 text-xs text-slate-400">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(conversation.updatedAt)}
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteConversation(conversation.id);
                          }}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );

  return (
    <div className="flex h-full bg-slate-50 dark:bg-slate-900">
      {/* Desktop Conversations Sidebar */}
      <div className="hidden lg:block w-80">
        <ConversationsSidebar />
      </div>

      {/* Mobile Conversations Sheet */}
      <Sheet
        open={isMobileConversationsOpen}
        onOpenChange={setIsMobileConversationsOpen}
      >
        <SheetContent side="left" className="w-80 p-0 lg:hidden">
          <ConversationsSidebar />
        </SheetContent>
      </Sheet>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Chat Header */}
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="lg:hidden">
                    <MessageSquare className="w-4 h-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-80 p-0">
                  <ConversationsSidebar />
                </SheetContent>
              </Sheet>

              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-slate-900 dark:text-white">
                    {currentConversation?.title || "Houston"}
                  </h1>
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    AI Assistant for {currentOrganization?.name}
                  </p>
                </div>
              </div>
            </div>

            <Badge
              variant="outline"
              className="border-green-200 text-green-700 bg-green-50"
            >
              {isStreaming ? "Typing..." : "Online"}
            </Badge>
          </div>
        </div>

        {/* Error Banner */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 p-3">
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setError(null)}
                className="ml-auto h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </div>
        )}

        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="max-w-4xl mx-auto space-y-6">
            {!currentConversation ? (
              <div className="text-center py-16">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                  Welcome to Houston
                </h3>
                <p className="text-slate-600 dark:text-slate-400 mb-6">
                  Your AI assistant for {currentOrganization?.name}. Start a new
                  conversation to begin.
                </p>
                <Button
                  onClick={createNewConversation}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Start New Conversation
                </Button>
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                  Let's start chatting
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  Ask me anything about your work, projects, or need help with
                  analysis.
                </p>
              </div>
            ) : (
              messages?.filter(Boolean).map((message) => (
                <div
                  key={message.id}
                  className={`flex items-start space-x-3 ${
                    message.role === "user"
                      ? "flex-row-reverse space-x-reverse"
                      : ""
                  }`}
                >
                  <Avatar className="w-8 h-8 flex-shrink-0">
                    {message.role === "user" ? (
                      <AvatarFallback className="bg-slate-100 dark:bg-slate-800">
                        <User className="w-4 h-4" />
                      </AvatarFallback>
                    ) : (
                      <AvatarFallback className="bg-gradient-to-br from-indigo-500 to-purple-600">
                        <Bot className="w-4 h-4 text-white" />
                      </AvatarFallback>
                    )}
                  </Avatar>

                  <div
                    className={`flex-1 space-y-2 ${
                      message.role === "user" ? "items-end" : ""
                    }`}
                  >
                    <div
                      className={`rounded-lg px-4 py-3 max-w-3xl ${
                        message.role === "user"
                          ? "bg-indigo-600 text-white ml-auto"
                          : "bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700"
                      }`}
                    >
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <StreamingMessage
                          content={streamingState.currentContent}
                          isStreaming={streamingState.isStreaming}
                        />
                        {message.role === "assistant" && !message.content && (
                          <div className="flex items-center">
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            <span className="text-xs text-slate-500 dark:text-slate-400">
                              {streamingState.loadingMessage ||
                                "Houston is Typing..."}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {message.role === "assistant" && (
                      <div className="flex flex-wrap gap-2">
                        <span className="text-xs text-gray-500 mr-2">
                          Sources:
                        </span>
                        {message.references?.map((source, index) => (
                          <SourceReference key={index} source={source} />
                        ))}
                      </div>
                    )}
                    {message.createdAt && (
                      <div
                        className={`text-xs text-slate-500 dark:text-slate-400 ${
                          message.role === "user" ? "text-right" : "text-left"
                        }`}
                      >
                        {formatTime(message.createdAt)}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
          <div ref={messagesEndRef} />
        </ScrollArea>

        {/* Input Area */}
        {currentConversation && (
          <div className="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-4">
            <div className="max-w-4xl mx-auto">
              {/* Settings Status */}
              {(chatSettings.enableWebSearch ||
                chatSettings.personality ||
                chatSettings.systemPrompt) && (
                <div className="flex items-center space-x-2 mb-3 text-xs">
                  <span className="text-slate-500">Active:</span>
                  {chatSettings.enableWebSearch && (
                    <Badge variant="secondary" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      Web Search
                    </Badge>
                  )}
                  {(chatSettings.personality || chatSettings.systemPrompt) && (
                    <Badge variant="secondary" className="text-xs">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Custom AI
                    </Badge>
                  )}
                </div>
              )}
              <div className="flex items-end space-x-2">
                <div className="flex-1 relative">
                  <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask Houston anything..."
                    disabled={isLoading || isStreaming}
                    className="pr-12 min-h-[44px] resize-none border-slate-200 dark:border-slate-700 focus:border-indigo-300"
                  />
                </div>
                <Button
                  variant={currentRequestWebSearch ? "default" : "outline"}
                  size="sm"
                  onClick={() =>
                    setCurrentRequestWebSearch(!currentRequestWebSearch)
                  }
                  disabled={isLoading || isStreaming}
                  className="h-11 px-3"
                  title={
                    currentRequestWebSearch
                      ? "Disable web search for this message"
                      : "Enable web search for this message"
                  }
                >
                  <Globe
                    className={`w-4 h-4 ${
                      currentRequestWebSearch ? "text-white" : "text-slate-600"
                    }`}
                  />
                </Button>
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading || isStreaming}
                  className="bg-indigo-600 hover:bg-indigo-700 h-11 px-4"
                >
                  {isLoading || isStreaming ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>

              <p className="text-xs text-slate-500 dark:text-slate-400 mt-2 text-center">
                Houston can make mistakes. Please verify important information.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
