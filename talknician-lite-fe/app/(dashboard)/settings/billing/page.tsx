"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { CreditCard, Download, Zap, Check, ArrowRight, Receipt } from "lucide-react"

export default function BillingPage() {
  const [currentPlan] = useState({
    name: "Professional",
    price: 299,
    billing: "monthly",
    seats: 12,
    maxSeats: 25,
    features: [
      "Unlimited Houston conversations",
      "Advanced integrations",
      "Priority support",
      "Custom branding",
      "Advanced analytics",
    ],
  })

  const [usage] = useState({
    seats: 12,
    maxSeats: 25,
    conversations: 1247,
    maxConversations: 5000,
    storage: 2.4,
    maxStorage: 10,
  })

  const [invoices] = useState([
    {
      id: "INV-2024-001",
      date: "2024-01-15",
      amount: 299,
      status: "paid",
      description: "Professional Plan - January 2024",
    },
    {
      id: "INV-2023-012",
      date: "2023-12-15",
      amount: 299,
      status: "paid",
      description: "Professional Plan - December 2023",
    },
    {
      id: "INV-2023-011",
      date: "2023-11-15",
      amount: 299,
      status: "paid",
      description: "Professional Plan - November 2023",
    },
  ])

  const plans = [
    {
      name: "Starter",
      price: 49,
      description: "Perfect for small teams getting started",
      features: [
        "Up to 5 team members",
        "1,000 conversations/month",
        "Basic integrations",
        "Email support",
        "2GB storage",
      ],
      current: false,
    },
    {
      name: "Professional",
      price: 299,
      description: "Advanced features for growing teams",
      features: [
        "Up to 25 team members",
        "5,000 conversations/month",
        "Advanced integrations",
        "Priority support",
        "10GB storage",
        "Custom branding",
      ],
      current: true,
      popular: true,
    },
    {
      name: "Enterprise",
      price: 999,
      description: "Full-featured solution for large organizations",
      features: [
        "Unlimited team members",
        "Unlimited conversations",
        "All integrations",
        "24/7 phone support",
        "Unlimited storage",
        "Advanced security",
        "Custom integrations",
      ],
      current: false,
    },
  ]

  return (
    <div className="flex flex-col h-full bg-slate-50 dark:bg-slate-900">
      {/* Header */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Plans & Billing</h1>
            <p className="text-slate-600 dark:text-slate-400">Manage your subscription and billing information</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download Invoice
            </Button>
            <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
              <CreditCard className="w-4 h-4 mr-2" />
              Update Payment
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 space-y-6 overflow-auto">
        {/* Current Plan */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>Current Plan</span>
              </div>
              <Badge variant="outline" className="border-indigo-200 text-indigo-700 bg-indigo-50">
                {currentPlan.name}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                  ${currentPlan.price}
                  <span className="text-lg font-normal text-slate-600 dark:text-slate-400">/{currentPlan.billing}</span>
                </h3>
                <p className="text-slate-600 dark:text-slate-400">Next billing: February 15, 2024</p>
              </div>
              <Button variant="outline">Change Plan</Button>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Team Members</span>
                  <span className="text-sm text-slate-900 dark:text-white">
                    {usage.seats} / {usage.maxSeats}
                  </span>
                </div>
                <Progress value={(usage.seats / usage.maxSeats) * 100} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Conversations</span>
                  <span className="text-sm text-slate-900 dark:text-white">
                    {usage.conversations.toLocaleString()} / {usage.maxConversations.toLocaleString()}
                  </span>
                </div>
                <Progress value={(usage.conversations / usage.maxConversations) * 100} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Storage</span>
                  <span className="text-sm text-slate-900 dark:text-white">
                    {usage.storage}GB / {usage.maxStorage}GB
                  </span>
                </div>
                <Progress value={(usage.storage / usage.maxStorage) * 100} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Available Plans */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle>Available Plans</CardTitle>
            <p className="text-slate-600 dark:text-slate-400">Choose the plan that best fits your team's needs</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan) => (
                <div
                  key={plan.name}
                  className={`relative p-6 rounded-lg border-2 transition-colors ${
                    plan.current
                      ? "border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-950"
                      : "border-slate-200 dark:border-slate-700"
                  }`}
                >
                  {plan.popular && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-indigo-600 text-white">
                      Most Popular
                    </Badge>
                  )}
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white">{plan.name}</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-sm mt-1">{plan.description}</p>
                    <div className="mt-4">
                      <span className="text-3xl font-bold text-slate-900 dark:text-white">${plan.price}</span>
                      <span className="text-slate-600 dark:text-slate-400">/month</span>
                    </div>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-slate-600 dark:text-slate-400">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${
                      plan.current ? "bg-slate-600 hover:bg-slate-700" : "bg-indigo-600 hover:bg-indigo-700 text-white"
                    }`}
                    disabled={plan.current}
                  >
                    {plan.current ? "Current Plan" : "Upgrade"}
                    {!plan.current && <ArrowRight className="w-4 h-4 ml-2" />}
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="w-5 h-5" />
              <span>Payment Method</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">VISA</span>
                </div>
                <div>
                  <p className="font-medium text-slate-900 dark:text-white">•••• •••• •••• 4242</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Expires 12/2025</p>
                </div>
              </div>
              <Button variant="outline">Update</Button>
            </div>
          </CardContent>
        </Card>

        {/* Billing History */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Receipt className="w-5 h-5" />
              <span>Billing History</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {invoices.map((invoice) => (
                <div
                  key={invoice.id}
                  className="flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center">
                      <Receipt className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <div>
                      <p className="font-medium text-slate-900 dark:text-white">{invoice.description}</p>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        {invoice.id} • {new Date(invoice.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium text-slate-900 dark:text-white">${invoice.amount}</p>
                      <Badge
                        variant="outline"
                        className={
                          invoice.status === "paid"
                            ? "border-green-200 text-green-700 bg-green-50"
                            : "border-orange-200 text-orange-700 bg-orange-50"
                        }
                      >
                        {invoice.status}
                      </Badge>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
