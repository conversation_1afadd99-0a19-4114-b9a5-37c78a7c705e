"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  HelpCircle,
  Search,
  MessageCircle,
  Mail,
  Phone,
  Book,
  Video,
  FileText,
  ExternalLink,
  ChevronRight,
  Clock,
  CheckCircle,
} from "lucide-react"

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [supportTicket, setSupportTicket] = useState({
    subject: "",
    category: "",
    priority: "",
    description: "",
  })

  const [tickets] = useState([
    {
      id: "TICK-001",
      subject: "Integration with Google Drive not working",
      status: "open",
      priority: "high",
      created: "2024-01-15",
      lastUpdate: "2024-01-16",
    },
    {
      id: "TICK-002",
      subject: "Question about billing cycle",
      status: "resolved",
      priority: "low",
      created: "2024-01-10",
      lastUpdate: "2024-01-12",
    },
  ])

  const faqs = [
    {
      question: "How do I invite team members to my organization?",
      answer: "Go to Organization > Team Members and click 'Invite Member'. Enter their email and select their role.",
      category: "Team Management",
    },
    {
      question: "What file types are supported for RAG processing?",
      answer: "We support PDF, DOC, DOCX, TXT, and most image formats. Files are processed automatically when added.",
      category: "Integrations",
    },
    {
      question: "How do I connect my Google Drive?",
      answer:
        "Navigate to Integrations > Cloud Storage and click 'Connect Google Drive'. Follow the OAuth flow to authorize access.",
      category: "Integrations",
    },
    {
      question: "Can I change my billing plan?",
      answer:
        "Yes, go to Settings > Plans & Billing to upgrade or downgrade your plan. Changes take effect immediately.",
      category: "Billing",
    },
    {
      question: "How does Houston use my documents?",
      answer:
        "Houston processes documents in your RAG system to provide contextual answers. Your data is encrypted and never shared.",
      category: "AI Assistant",
    },
  ]

  const resources = [
    {
      title: "Getting Started Guide",
      description: "Complete guide to setting up your organization and inviting team members",
      type: "guide",
      icon: Book,
      url: "#",
    },
    {
      title: "Houston AI Tutorial",
      description: "Learn how to get the most out of your AI assistant",
      type: "video",
      icon: Video,
      url: "#",
    },
    {
      title: "API Documentation",
      description: "Technical documentation for developers and integrations",
      type: "docs",
      icon: FileText,
      url: "#",
    },
    {
      title: "Security & Privacy",
      description: "Learn about our security measures and data protection",
      type: "guide",
      icon: Book,
      url: "#",
    },
  ]

  const handleSubmitTicket = () => {
    console.log("Support ticket submitted:", supportTicket)
    setSupportTicket({ subject: "", category: "", priority: "", description: "" })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "open":
        return (
          <Badge variant="outline" className="border-orange-200 text-orange-700 bg-orange-50">
            Open
          </Badge>
        )
      case "resolved":
        return (
          <Badge variant="outline" className="border-green-200 text-green-700 bg-green-50">
            Resolved
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <Badge variant="outline" className="border-red-200 text-red-700 bg-red-50">
            High
          </Badge>
        )
      case "medium":
        return (
          <Badge variant="outline" className="border-yellow-200 text-yellow-700 bg-yellow-50">
            Medium
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="border-slate-200 text-slate-700 bg-slate-50">
            Low
          </Badge>
        )
      default:
        return <Badge variant="outline">Normal</Badge>
    }
  }

  return (
    <div className="flex flex-col h-full bg-slate-50 dark:bg-slate-900">
      {/* Header */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Help & Support</h1>
            <p className="text-slate-600 dark:text-slate-400">Get help, find answers, and contact our support team</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <MessageCircle className="w-4 h-4 mr-2" />
              Live Chat
            </Button>
            <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
              <Mail className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mt-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search help articles, FAQs, and documentation..."
            className="pl-10 border-slate-200 focus:border-indigo-300"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 space-y-6 overflow-auto">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <MessageCircle className="w-8 h-8 text-indigo-600 mx-auto mb-3" />
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Live Chat</h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">Get instant help from our support team</p>
              <Badge variant="outline" className="border-green-200 text-green-700 bg-green-50 mt-2">
                Online
              </Badge>
            </CardContent>
          </Card>
          <Card className="border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Mail className="w-8 h-8 text-indigo-600 mx-auto mb-3" />
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Email Support</h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">Send us a detailed message</p>
              <Badge variant="outline" className="border-slate-200 text-slate-700 bg-slate-50 mt-2">
                24h response
              </Badge>
            </CardContent>
          </Card>
          <Card className="border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Phone className="w-8 h-8 text-indigo-600 mx-auto mb-3" />
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Phone Support</h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">Call us for urgent issues</p>
              <Badge variant="outline" className="border-indigo-200 text-indigo-700 bg-indigo-50 mt-2">
                Pro Plan Only
              </Badge>
            </CardContent>
          </Card>
        </div>

        {/* Frequently Asked Questions */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <HelpCircle className="w-5 h-5" />
              <span>Frequently Asked Questions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index} className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-slate-900 dark:text-white mb-2">{faq.question}</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">{faq.answer}</p>
                      <Badge variant="outline" className="text-xs">
                        {faq.category}
                      </Badge>
                    </div>
                    <ChevronRight className="w-4 h-4 text-slate-400 ml-2" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Resources */}
        <Card className="border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle>Documentation & Resources</CardTitle>
            <p className="text-slate-600 dark:text-slate-400">
              Guides, tutorials, and documentation to help you succeed
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {resources.map((resource, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-4 p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 cursor-pointer"
                >
                  <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                    <resource.icon className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-slate-900 dark:text-white">{resource.title}</h4>
                    <p className="text-sm text-slate-600 dark:text-slate-400">{resource.description}</p>
                  </div>
                  <ExternalLink className="w-4 h-4 text-slate-400" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Support Tickets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Create Ticket */}
          <Card className="border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle>Create Support Ticket</CardTitle>
              <p className="text-slate-600 dark:text-slate-400">Need personalized help? Submit a support ticket</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  value={supportTicket.subject}
                  onChange={(e) => setSupportTicket({ ...supportTicket, subject: e.target.value })}
                  placeholder="Brief description of your issue"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={supportTicket.category}
                    onValueChange={(value) => setSupportTicket({ ...supportTicket, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technical">Technical Issue</SelectItem>
                      <SelectItem value="billing">Billing</SelectItem>
                      <SelectItem value="account">Account</SelectItem>
                      <SelectItem value="integrations">Integrations</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={supportTicket.priority}
                    onValueChange={(value) => setSupportTicket({ ...supportTicket, priority: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={supportTicket.description}
                  onChange={(e) => setSupportTicket({ ...supportTicket, description: e.target.value })}
                  placeholder="Provide detailed information about your issue..."
                  rows={4}
                />
              </div>
              <Button onClick={handleSubmitTicket} className="w-full bg-indigo-600 hover:bg-indigo-700 text-white">
                Submit Ticket
              </Button>
            </CardContent>
          </Card>

          {/* My Tickets */}
          <Card className="border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle>My Support Tickets</CardTitle>
              <p className="text-slate-600 dark:text-slate-400">Track your support requests</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tickets.map((ticket) => (
                  <div key={ticket.id} className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-slate-900 dark:text-white">{ticket.subject}</h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">#{ticket.id}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(ticket.status)}
                        {getPriorityBadge(ticket.priority)}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-slate-500 dark:text-slate-400">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>Created {new Date(ticket.created).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-3 h-3" />
                        <span>Updated {new Date(ticket.lastUpdate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
