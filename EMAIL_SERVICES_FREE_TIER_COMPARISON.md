# 📧 Email Services Free Tier Comparison & Implementation Guide

## **🎯 Top Email Services with Free Tiers (2024)**

Based on Context7 research, here are the best email services ranked by their free tier offerings:

---

## **🥇 1. Postmark (RECOMMENDED for Startups)**

**Free Tier**: **100 emails/month forever**
**Paid**: $1.25/1000 emails after free tier

### **Why Postmark is #1:**

- ✅ **Best deliverability** (99%+ inbox rate)
- ✅ **Detailed analytics** and bounce tracking
- ✅ **Developer-friendly** API and documentation
- ✅ **No daily limits** - use all 100 emails anytime
- ✅ **Professional templates** included
- ✅ **Excellent support** even on free tier

### **Setup with Node.js:**

```bash
npm install postmark
```

```javascript
// src/services/postmark.service.js
const postmark = require("postmark");

class PostmarkService {
  constructor() {
    this.client = new postmark.ServerClient(process.env.POSTMARK_SERVER_TOKEN);
  }

  async sendEmail(to, subject, htmlBody, textBody) {
    try {
      const result = await this.client.sendEmail({
        From: process.env.POSTMARK_FROM_EMAIL,
        To: to,
        Subject: subject,
        HtmlBody: htmlBody,
        TextBody: textBody,
        MessageStream: "outbound",
      });

      console.log("Email sent successfully:", result.MessageID);
      return result;
    } catch (error) {
      console.error("Failed to send email:", error);
      throw error;
    }
  }

  async sendVerificationEmail(to, verificationLink) {
    const html = `
      <h2>Verify Your Email</h2>
      <p>Click the link below to verify your email address:</p>
      <a href="${verificationLink}" style="background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Verify Email</a>
    `;

    return await this.sendEmail(
      to,
      "Verify Your Email",
      html,
      `Verify your email: ${verificationLink}`
    );
  }

  async sendInvitationEmail(to, organizationName, inviteLink) {
    const html = `
      <h2>You're Invited to Join ${organizationName}</h2>
      <p>You've been invited to join ${organizationName} on Talknician.</p>
      <a href="${inviteLink}" style="background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Join Organization</a>
    `;

    return await this.sendEmail(
      to,
      `Invitation to ${organizationName}`,
      html,
      `Join ${organizationName}: ${inviteLink}`
    );
  }
}

module.exports = new PostmarkService();
```

### **Environment Variables:**

```bash
POSTMARK_SERVER_TOKEN="your-server-token"
POSTMARK_FROM_EMAIL="<EMAIL>"
```

---

## **🥈 2. Mailgun (Best for Developers)**

**Free Tier**: **5,000 emails/month for 3 months**
**Paid**: $0.80/1000 emails

### **Why Mailgun is Great:**

- ✅ **Generous free tier** (5k emails/month)
- ✅ **Powerful API** with advanced features
- ✅ **Email validation** included
- ✅ **Detailed logs** and analytics
- ✅ **EU data residency** available
- ❌ **Time-limited** free tier (3 months only)

### **Setup with Node.js:**

```bash
npm install mailgun.js form-data
```

```javascript
// src/services/mailgun.service.js
const formData = require("form-data");
const Mailgun = require("mailgun.js");

class MailgunService {
  constructor() {
    const mailgun = new Mailgun(formData);
    this.client = mailgun.client({
      username: "api",
      key: process.env.MAILGUN_API_KEY,
      url: "https://api.mailgun.net", // or 'https://api.eu.mailgun.net' for EU
    });
    this.domain = process.env.MAILGUN_DOMAIN;
  }

  async sendEmail(to, subject, html, text) {
    try {
      const messageData = {
        from: process.env.MAILGUN_FROM_EMAIL,
        to: to,
        subject: subject,
        html: html,
        text: text,
      };

      const result = await this.client.messages.create(
        this.domain,
        messageData
      );
      console.log("Email sent successfully:", result.id);
      return result;
    } catch (error) {
      console.error("Failed to send email:", error);
      throw error;
    }
  }

  async sendPasswordResetEmail(to, resetLink) {
    const html = `
      <h2>Reset Your Password</h2>
      <p>Click the link below to reset your password:</p>
      <a href="${resetLink}" style="background: #EF4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Reset Password</a>
      <p><small>This link expires in 1 hour.</small></p>
    `;

    return await this.sendEmail(
      to,
      "Reset Your Password",
      html,
      `Reset your password: ${resetLink}`
    );
  }
}

module.exports = new MailgunService();
```

### **Environment Variables:**

```bash
MAILGUN_API_KEY="your-api-key"
MAILGUN_DOMAIN="your-domain.com"
MAILGUN_FROM_EMAIL="<EMAIL>"
```

---

## **🥉 3. SendGrid (Most Popular)**

**Free Tier**: **100 emails/day (3,000/month)**
**Paid**: $14.95/month (50k emails)

### **Why SendGrid:**

- ✅ **Popular and reliable**
- ✅ **Good documentation**
- ✅ **Template management**
- ✅ **Marketing features**
- ❌ **Daily limits** (can't send all at once)
- ❌ **More expensive** than competitors

### **Setup with Node.js:**

```bash
npm install @sendgrid/mail
```

```javascript
// src/services/sendgrid.service.js
const sgMail = require("@sendgrid/mail");

class SendGridService {
  constructor() {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  async sendEmail(to, subject, html, text) {
    try {
      const msg = {
        to: to,
        from: process.env.SENDGRID_FROM_EMAIL,
        subject: subject,
        text: text,
        html: html,
      };

      const result = await sgMail.send(msg);
      console.log("Email sent successfully");
      return result;
    } catch (error) {
      console.error("Failed to send email:", error);
      throw error;
    }
  }
}

module.exports = new SendGridService();
```

---

## **🏆 4. Nodemailer + Free SMTP (Budget Option)**

**Free Tier**: **Varies by provider**
**Cost**: **Free** with Gmail, Outlook, etc.

### **Free SMTP Providers:**

- **Gmail**: 500 emails/day
- **Outlook**: 300 emails/day
- **Yahoo**: 500 emails/day
- **Zoho**: 250 emails/day

### **Setup with Nodemailer:**

```bash
npm install nodemailer
```

```javascript
// src/services/nodemailer.service.js
const nodemailer = require("nodemailer");

class NodemailerService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      service: "gmail", // or 'outlook', 'yahoo', etc.
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS, // Use App Password for Gmail
      },
    });
  }

  async sendEmail(to, subject, html, text) {
    try {
      const mailOptions = {
        from: process.env.SMTP_FROM,
        to: to,
        subject: subject,
        text: text,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log("Email sent successfully:", result.messageId);
      return result;
    } catch (error) {
      console.error("Failed to send email:", error);
      throw error;
    }
  }
}

module.exports = new NodemailerService();
```

---

## **📊 Free Tier Comparison Table**

| Service                | Free Emails | Duration | Daily Limit | Best For                |
| ---------------------- | ----------- | -------- | ----------- | ----------------------- |
| **Postmark**           | 100/month   | Forever  | No          | **Production apps**     |
| **Mailgun**            | 5,000/month | 3 months | No          | **Development/Testing** |
| **SendGrid**           | 100/day     | Forever  | Yes         | **Marketing emails**    |
| **Nodemailer + Gmail** | 500/day     | Forever  | Yes         | **Personal projects**   |

---

## **🚀 Implementation in Your Project**

### **1. Create Email Service Factory:**

```javascript
// src/services/email.factory.js
const PostmarkService = require("./postmark.service");
const MailgunService = require("./mailgun.service");
const SendGridService = require("./sendgrid.service");
const NodemailerService = require("./nodemailer.service");

class EmailServiceFactory {
  static create() {
    const provider = process.env.EMAIL_PROVIDER || "postmark";

    switch (provider.toLowerCase()) {
      case "postmark":
        return PostmarkService;
      case "mailgun":
        return MailgunService;
      case "sendgrid":
        return SendGridService;
      case "nodemailer":
        return NodemailerService;
      default:
        throw new Error(`Unknown email provider: ${provider}`);
    }
  }
}

module.exports = EmailServiceFactory;
```

### **2. Update Your Auth Routes:**

```javascript
// src/routes/auth.routes.ts
import EmailServiceFactory from "../services/email.factory";

const emailService = EmailServiceFactory.create();

// In your signup endpoint:
await emailService.sendVerificationEmail(user.email, verificationLink);

// In your invitation endpoint:
await emailService.sendInvitationEmail(email, organizationName, inviteLink);
```

### **3. Environment Configuration:**

```bash
# Choose your provider
EMAIL_PROVIDER=postmark

# Postmark
POSTMARK_SERVER_TOKEN=your-token
POSTMARK_FROM_EMAIL=<EMAIL>

# Mailgun
MAILGUN_API_KEY=your-key
MAILGUN_DOMAIN=yourdomain.com
MAILGUN_FROM_EMAIL=<EMAIL>

# SendGrid
SENDGRID_API_KEY=your-key
SENDGRID_FROM_EMAIL=<EMAIL>

# Nodemailer
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

---

## **🎯 My Recommendation for Talknician**

**Start with Postmark** because:

1. **Perfect for MVP**: 100 emails/month is ideal for early testing
2. **No time pressure**: Free tier is permanent
3. **Best deliverability**: Your emails actually reach inboxes
4. **Professional**: Looks good to investors/users
5. **Easy scaling**: Simple upgrade path when you grow

**Backup plan**: Set up Mailgun as well for the 3-month 5k email boost during launch.

**Implementation order**:

1. ✅ Fix auto-join (already done)
2. 🔄 Set up Postmark (recommended first)
3. 🔄 Test with verification emails
4. 🔄 Add invitation emails
5. 🔄 Monitor usage and upgrade when needed

Would you like me to help you set up Postmark first, or do you prefer to start with a different service?
