# Account Linking & Authentication Fix Guide

## Issues Fixed

### 1. ✅ **Auth Token Disappearing in Social Login**

**Problem**: When users logged in with Google/Microsoft, the `auth_token` would appear briefly then disappear, leaving only Auth0 SPA cache entries.

**Root Cause**: Race condition between:

- Social login completing and storing token
- `initializeAuth()` useEffect running and clearing "invalid" tokens

**Solution**:

- Restructured `initializeAuth()` to prioritize Auth0 authentication status
- Added proper sequence: Check Auth0 → Sync with backend → Store token
- Added detailed logging for debugging
- Eliminated duplicate sync calls in social login functions

### 2. ✅ **Account Linking Already Implemented**

**Current Status**: Account linking is **ALREADY WORKING** in your backend!

The `findOrCreateUserWithAccountLinking()` function automatically:

- Links social accounts with existing email-based accounts
- Creates new accounts if email doesn't exist
- Updates Auth0 IDs for existing users
- Preserves organization memberships

## How Account Linking Works

### Backend Logic (Already Implemented)

```typescript
// In talknician-lite-be/src/routes/auth.routes.ts
async function findOrCreateUserWithAccountLinking(auth0User: any) {
  // 1. Try to find user by Auth0 ID
  let user = await prisma.user.findUnique({
    where: { auth0Id: auth0User.user_id },
  });

  if (user) {
    return user; // Existing social login user
  }

  // 2. If not found by Auth0 ID, check by email (ACCOUNT LINKING)
  const existingUserByEmail = await prisma.user.findUnique({
    where: { email: auth0User.email },
  });

  if (existingUserByEmail) {
    // 🔗 LINK ACCOUNTS - Update existing user with Auth0 ID
    user = await prisma.user.update({
      where: { id: existingUserByEmail.id },
      data: {
        auth0Id: auth0User.user_id,
        name: auth0User.name || existingUserByEmail.name,
        avatar: auth0User.picture || existingUserByEmail.avatar,
      },
    });

    logger.info("🔗 ACCOUNT LINKED - user found by email", {
      userId: user.id,
      email: user.email,
      auth0Id: auth0User.user_id,
      accountLinking: true,
    });

    return user;
  }

  // 3. Create new user if email doesn't exist
  // ... create new user logic
}
```

### Account Linking Scenarios

#### Scenario 1: User registers with email/password first

1. User creates account: `<EMAIL>` with password
2. Later, user clicks "Continue with Google" using same email
3. System finds existing user by email
4. **Links Google account to existing user**
5. User can now login with either method

#### Scenario 2: User signs in with Google first

1. User clicks "Continue with Google": `<EMAIL>`
2. System creates new user with Google Auth0 ID
3. Later, user tries to register with same email/password
4. System detects existing email and shows appropriate message

## Frontend Authentication Flow

### Fixed Social Login Process

```typescript
// Fixed sequence in AuthContext.tsx
1. User clicks "Continue with Google"
2. Auth0 popup opens and completes
3. initializeAuth() detects Auth0 authentication
4. Calls social-login-sync endpoint
5. Backend performs account linking if needed
6. Token stored in localStorage as 'auth_token'
7. User data updated with backend response
```

### Key Improvements Made

1. **Eliminated Race Conditions**

   - Social login now handled in `initializeAuth()`
   - No duplicate sync calls
   - Proper sequence handling

2. **Better Error Handling**

   - Fallback to Auth0 data if backend sync fails
   - Comprehensive logging for debugging
   - Graceful handling of network issues

3. **Improved User Experience**
   - Seamless account linking (no user action needed)
   - Preserves organization memberships
   - Works with both Google and Microsoft

## Testing Account Linking

### Test Case 1: Email → Social Login

1. Register with email: `<EMAIL>` / password
2. Login and verify access to organizations
3. Logout
4. Click "Continue with Google" using `<EMAIL>`
5. **Expected**: Same user, same organizations, can use either login method

### Test Case 2: Social → Email Registration

1. Use "Continue with Google" with `<EMAIL>`
2. Complete organization setup
3. Logout and clear Auth0 cache
4. Try to register with `<EMAIL>` and password
5. **Expected**: System should handle appropriately (likely show "email already exists")

## Debug Information

### Browser Console Logs

After implementing the fix, you'll see detailed logs:

```
Auth0 authenticated - processing social login
New social login token detected, syncing...
Social login sync successful
🔗 ACCOUNT LINKED - user found by email (in backend logs)
```

### Backend Logs

The backend logs will show:

```json
{
  "message": "🔗 ACCOUNT LINKED - user found by email",
  "userId": "123",
  "email": "<EMAIL>",
  "auth0Id": "google-oauth2|...",
  "accountLinking": true
}
```

## Next Steps

1. **Test the fixes** - Social login should now maintain auth_token
2. **Verify account linking** - Test with existing users
3. **Monitor logs** - Check both frontend console and backend logs
4. **Optional enhancements**:
   - Add user notification when accounts are linked
   - Add account management UI to show linked providers
   - Add option to unlink accounts

## Authentication Methods Summary

| Method             | Status     | Account Linking |
| ------------------ | ---------- | --------------- |
| Email/Password     | ✅ Working | Source account  |
| Google OAuth       | ✅ Fixed   | Links to email  |
| Microsoft OAuth    | ✅ Fixed   | Links to email  |
| Token Refresh      | ✅ Working | All methods     |
| Email Verification | ✅ Working | All methods     |

The system now provides seamless account linking where users can use any authentication method to access the same account, preserving all their data and organization memberships.
